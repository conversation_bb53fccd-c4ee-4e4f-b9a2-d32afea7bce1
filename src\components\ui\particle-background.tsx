"use client";

import React, { useEffect, useRef, useState } from "react";
import { motion } from "framer-motion";
import { cn } from "@/lib/utils";

interface Particle {
  id: number;
  x: number;
  y: number;
  size: number;
  color: string;
  opacity: number;
  speed: number;
}

interface ParticleBackgroundProps {
  className?: string;
  particleCount?: number;
  particleColors?: string[];
  minSize?: number;
  maxSize?: number;
  minOpacity?: number;
  maxOpacity?: number;
  minSpeed?: number;
  maxSpeed?: number;
  direction?: "up" | "down";
  interactive?: boolean;
}

export const ParticleBackground: React.FC<ParticleBackgroundProps> = ({
  className,
  particleCount = 50,
  particleColors = ["#FF0080", "#FF5C00", "#FF0000", "#7928CA", "#0070F3"],
  minSize = 1,
  maxSize = 4,
  minOpacity = 0.1,
  maxOpacity = 0.5,
  minSpeed = 20,
  maxSpeed = 60,
  direction = "up",
  interactive = true,
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [particles, setParticles] = useState<Particle[]>([]);
  const [dimensions, setDimensions] = useState({ width: 0, height: 0 });
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [isHovering, setIsHovering] = useState(false);

  // Initialize particles
  useEffect(() => {
    if (!containerRef.current) return;
    
    const { offsetWidth, offsetHeight } = containerRef.current;
    setDimensions({ width: offsetWidth, height: offsetHeight });
    
    const newParticles: Particle[] = [];
    
    for (let i = 0; i < particleCount; i++) {
      newParticles.push({
        id: i,
        x: Math.random() * offsetWidth,
        y: Math.random() * offsetHeight,
        size: Math.random() * (maxSize - minSize) + minSize,
        color: particleColors[Math.floor(Math.random() * particleColors.length)],
        opacity: Math.random() * (maxOpacity - minOpacity) + minOpacity,
        speed: Math.random() * (maxSpeed - minSpeed) + minSpeed,
      });
    }
    
    setParticles(newParticles);
    
    const handleResize = () => {
      if (!containerRef.current) return;
      const { offsetWidth, offsetHeight } = containerRef.current;
      setDimensions({ width: offsetWidth, height: offsetHeight });
    };
    
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, [particleCount, particleColors, minSize, maxSize, minOpacity, maxOpacity, minSpeed, maxSpeed]);

  // Handle mouse movement for interactive particles
  useEffect(() => {
    if (!interactive) return;
    
    const handleMouseMove = (e: MouseEvent) => {
      if (!containerRef.current) return;
      
      const rect = containerRef.current.getBoundingClientRect();
      setMousePosition({
        x: e.clientX - rect.left,
        y: e.clientY - rect.top,
      });
    };
    
    const handleMouseEnter = () => setIsHovering(true);
    const handleMouseLeave = () => setIsHovering(false);
    
    const container = containerRef.current;
    if (container) {
      container.addEventListener("mousemove", handleMouseMove);
      container.addEventListener("mouseenter", handleMouseEnter);
      container.addEventListener("mouseleave", handleMouseLeave);
      
      return () => {
        container.removeEventListener("mousemove", handleMouseMove);
        container.removeEventListener("mouseenter", handleMouseEnter);
        container.removeEventListener("mouseleave", handleMouseLeave);
      };
    }
  }, [interactive]);

  return (
    <div
      ref={containerRef}
      className={cn("absolute inset-0 overflow-hidden pointer-events-none", className)}
    >
      {particles.map((particle) => (
        <motion.div
          key={particle.id}
          className="absolute rounded-full pointer-events-none"
          style={{
            width: particle.size,
            height: particle.size,
            backgroundColor: particle.color,
            opacity: particle.opacity,
            x: particle.x,
            y: particle.y,
          }}
          animate={{
            y: direction === "up" 
              ? [particle.y, -particle.size] 
              : [particle.y, dimensions.height + particle.size],
            x: isHovering && interactive
              ? particle.x + (mousePosition.x - particle.x) * 0.05
              : particle.x,
          }}
          transition={{
            y: {
              duration: dimensions.height / particle.speed,
              repeat: Infinity,
              repeatType: "loop",
              ease: "linear",
            },
            x: {
              duration: 0.5,
              ease: "easeOut",
            },
          }}
        />
      ))}
    </div>
  );
}; 