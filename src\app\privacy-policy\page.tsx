'use client'

import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { ArrowLeft, Shield, Eye, Lock, Database, UserCheck, FileText, List } from "lucide-react"
import { motion } from "framer-motion"

export default function PrivacyPolicy() {
  return (
    <div className="flex flex-col min-h-screen bg-gray-900 text-white font-sans">
      <main className="flex-grow">
        {/* Header Section */}
        <section className="relative py-20 px-4 md:px-6 lg:px-8 bg-gradient-to-br from-black via-gray-900 to-black overflow-hidden">
          {/* Background Effects */}
          <div className="absolute inset-0 bg-gradient-to-r from-red-500/5 via-purple-500/5 to-blue-500/5"></div>
          <div className="absolute top-0 left-1/4 w-96 h-96 bg-red-500/10 rounded-full blur-3xl"></div>
          <div className="absolute bottom-0 right-1/4 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl"></div>

          <div className="container mx-auto relative z-10">
            <motion.div
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="text-center max-w-4xl mx-auto"
            >
              <Link href="/" className="inline-flex items-center text-red-400 hover:text-red-300 mb-8 group">
                <ArrowLeft className="w-5 h-5 mr-2 group-hover:-translate-x-1 transition-transform" />
                Back to Home
              </Link>

              <div className="mb-6">
                <Shield className="w-16 h-16 text-red-400 mx-auto mb-4" />
              </div>

              <h1 className="text-5xl md:text-7xl font-bold mb-6 bg-clip-text text-transparent bg-gradient-to-r from-red-400 via-orange-500 to-red-600">
                Privacy Policy
              </h1>
              <p className="text-xl md:text-2xl text-gray-300 leading-relaxed">
                Your privacy is important to us. This policy explains how we collect, use, and protect your information.
              </p>

              <div className="mt-8 flex justify-center">
                <div className="bg-gradient-to-r from-red-500/20 to-purple-500/20 rounded-full px-6 py-2 border border-red-500/30">
                  <span className="text-red-300 text-sm font-medium">Last Updated: January 25, 2025</span>
                </div>
              </div>
            </motion.div>
          </div>
        </section>

        {/* Content Section */}
        <section className="py-20 px-4 md:px-6 lg:px-8 bg-gray-900 relative">
          <div className="container mx-auto max-w-6xl">
            <div className="flex flex-col lg:flex-row gap-12">
              {/* Table of Contents - Sticky Sidebar */}
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.8, delay: 0.2 }}
                className="lg:w-1/4"
              >
                <div className="lg:sticky lg:top-8">
                  <div className="bg-gradient-to-br from-gray-800/70 to-black/90 rounded-2xl p-6 border border-gray-700/50">
                    <h3 className="text-xl font-bold text-red-400 mb-6 flex items-center">
                      <List className="w-5 h-5 mr-2" />
                      Quick Navigation
                    </h3>
                    <nav className="space-y-3">
                      {[
                        { title: "Information We Collect", href: "#collect" },
                        { title: "How We Use Information", href: "#usage" },
                        { title: "Information Sharing", href: "#sharing" },
                        { title: "Data Security", href: "#security" },
                        { title: "Your Rights", href: "#rights" },
                        { title: "Contact Information", href: "#contact" }
                      ].map((item, index) => (
                        <a
                          key={index}
                          href={item.href}
                          className="block text-gray-300 hover:text-red-400 transition-colors text-sm py-2 px-3 rounded-lg hover:bg-gray-700/30"
                        >
                          {item.title}
                        </a>
                      ))}
                    </nav>
                  </div>
                </div>
              </motion.div>

              {/* Main Content */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.3 }}
                className="lg:w-3/4 space-y-12"
              >

              {/* Information We Collect */}
              <motion.div
                id="collect"
                initial={{ opacity: 0, x: -20 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6 }}
                viewport={{ once: true }}
                className="group"
              >
                <div className="bg-gradient-to-br from-gray-800/70 via-gray-800/50 to-black/90 rounded-2xl p-8 border border-gray-700/50 hover:border-red-500/30 transition-all duration-500 hover:shadow-2xl hover:shadow-red-500/10">
                  <div className="flex items-center mb-8">
                    <div className="p-3 bg-gradient-to-br from-red-500/20 to-purple-500/20 rounded-xl mr-4 group-hover:scale-110 transition-transform duration-300">
                      <Database className="w-8 h-8 text-red-400" />
                    </div>
                    <h2 className="text-3xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-red-400 to-orange-500">
                      Information We Collect
                    </h2>
                  </div>
                  <div className="space-y-6 text-gray-300">
                    <p className="text-lg leading-relaxed">We collect information you provide directly to us, such as:</p>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="bg-gray-700/30 rounded-lg p-4 hover:bg-gray-700/50 transition-colors">
                        <div className="flex items-center mb-2">
                          <div className="w-2 h-2 bg-red-400 rounded-full mr-3"></div>
                          <span className="font-semibold text-red-300">Personal Information</span>
                        </div>
                        <p className="text-sm text-gray-400 ml-5">Name, email address, phone number</p>
                      </div>
                      <div className="bg-gray-700/30 rounded-lg p-4 hover:bg-gray-700/50 transition-colors">
                        <div className="flex items-center mb-2">
                          <div className="w-2 h-2 bg-red-400 rounded-full mr-3"></div>
                          <span className="font-semibold text-red-300">Business Information</span>
                        </div>
                        <p className="text-sm text-gray-400 ml-5">Company name, industry, size</p>
                      </div>
                      <div className="bg-gray-700/30 rounded-lg p-4 hover:bg-gray-700/50 transition-colors">
                        <div className="flex items-center mb-2">
                          <div className="w-2 h-2 bg-red-400 rounded-full mr-3"></div>
                          <span className="font-semibold text-red-300">Technical Information</span>
                        </div>
                        <p className="text-sm text-gray-400 ml-5">IP address, browser type, device info</p>
                      </div>
                      <div className="bg-gray-700/30 rounded-lg p-4 hover:bg-gray-700/50 transition-colors">
                        <div className="flex items-center mb-2">
                          <div className="w-2 h-2 bg-red-400 rounded-full mr-3"></div>
                          <span className="font-semibold text-red-300">Usage Data</span>
                        </div>
                        <p className="text-sm text-gray-400 ml-5">How you interact with our services</p>
                      </div>
                    </div>
                  </div>
                </div>
              </motion.div>

              {/* How We Use Information */}
              <motion.div
                id="usage"
                initial={{ opacity: 0, x: 20 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
                viewport={{ once: true }}
                className="group"
              >
                <div className="bg-gradient-to-br from-gray-800/70 via-gray-800/50 to-black/90 rounded-2xl p-8 border border-gray-700/50 hover:border-blue-500/30 transition-all duration-500 hover:shadow-2xl hover:shadow-blue-500/10">
                  <div className="flex items-center mb-8">
                    <div className="p-3 bg-gradient-to-br from-blue-500/20 to-cyan-500/20 rounded-xl mr-4 group-hover:scale-110 transition-transform duration-300">
                      <Eye className="w-8 h-8 text-blue-400" />
                    </div>
                    <h2 className="text-3xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-blue-400 to-cyan-500">
                      How We Use Your Information
                    </h2>
                  </div>
                  <div className="space-y-6 text-gray-300">
                    <p className="text-lg leading-relaxed">We use the information we collect to:</p>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {[
                        "Provide, maintain, and improve our services",
                        "Process transactions and send related information",
                        "Send technical notices, updates, and support messages",
                        "Respond to your comments, questions, and customer service requests",
                        "Communicate with you about products, services, and events",
                        "Monitor and analyze trends, usage, and activities"
                      ].map((item, index) => (
                        <div key={index} className="bg-gray-700/30 rounded-lg p-4 hover:bg-gray-700/50 transition-colors">
                          <div className="flex items-start">
                            <div className="w-2 h-2 bg-blue-400 rounded-full mr-3 mt-2 flex-shrink-0"></div>
                            <span className="text-gray-300">{item}</span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </motion.div>

              {/* Information Sharing */}
              <div className="bg-gradient-to-br from-gray-800/70 to-black/90 rounded-xl p-8 border border-gray-700/50">
                <div className="flex items-center mb-6">
                  <UserCheck className="w-8 h-8 text-red-400 mr-3" />
                  <h2 className="text-2xl font-bold text-red-400">Information Sharing</h2>
                </div>
                <div className="space-y-4 text-gray-300">
                  <p>We do not sell, trade, or otherwise transfer your personal information to third parties except:</p>
                  <ul className="list-disc list-inside space-y-2 ml-4">
                    <li>With your consent</li>
                    <li>To trusted service providers who assist us in operating our business</li>
                    <li>When required by law or to protect our rights</li>
                    <li>In connection with a merger, acquisition, or sale of assets</li>
                  </ul>
                </div>
              </div>

              {/* Data Security */}
              <div className="bg-gradient-to-br from-gray-800/70 to-black/90 rounded-xl p-8 border border-gray-700/50">
                <div className="flex items-center mb-6">
                  <Lock className="w-8 h-8 text-red-400 mr-3" />
                  <h2 className="text-2xl font-bold text-red-400">Data Security</h2>
                </div>
                <div className="space-y-4 text-gray-300">
                  <p>
                    We implement appropriate technical and organizational security measures to protect your personal 
                    information against unauthorized access, alteration, disclosure, or destruction. These measures include:
                  </p>
                  <ul className="list-disc list-inside space-y-2 ml-4">
                    <li>Encryption of data in transit and at rest</li>
                    <li>Regular security assessments and updates</li>
                    <li>Access controls and authentication measures</li>
                    <li>Employee training on data protection</li>
                  </ul>
                </div>
              </div>

              {/* Your Rights */}
              <div className="bg-gradient-to-br from-gray-800/70 to-black/90 rounded-xl p-8 border border-gray-700/50">
                <div className="flex items-center mb-6">
                  <Shield className="w-8 h-8 text-red-400 mr-3" />
                  <h2 className="text-2xl font-bold text-red-400">Your Rights</h2>
                </div>
                <div className="space-y-4 text-gray-300">
                  <p>You have the right to:</p>
                  <ul className="list-disc list-inside space-y-2 ml-4">
                    <li>Access and receive a copy of your personal information</li>
                    <li>Rectify inaccurate or incomplete personal information</li>
                    <li>Request deletion of your personal information</li>
                    <li>Object to or restrict the processing of your personal information</li>
                    <li>Data portability</li>
                    <li>Withdraw consent at any time</li>
                  </ul>
                </div>
              </div>

              {/* Contact Information */}
              <div className="bg-gradient-to-br from-gray-800/70 to-black/90 rounded-xl p-8 border border-gray-700/50">
                <h2 className="text-2xl font-bold text-red-400 mb-6">Contact Us</h2>
                <div className="space-y-4 text-gray-300">
                  <p>
                    If you have any questions about this Privacy Policy, please contact us at:
                  </p>
                  <div className="space-y-2">
                    <p><strong>Email:</strong> <EMAIL></p>
                    <p><strong>Address:</strong> Inflynx Technologies, Bhubaneswar, Odisha, India</p>
                    <p><strong>Phone:</strong> +91-6202620644</p>
                  </div>
                </div>
              </div>

              {/* Back to Home Button */}
              <div className="text-center pt-8">
                <Link href="/">
                  <Button className="bg-red-500 hover:bg-red-600 text-white px-8 py-3">
                    Back to Home
                  </Button>
                </Link>
              </div>
              </motion.div>
            </div>
          </div>
        </section>
      </main>

      {/* Floating Action Button */}
      <motion.div
        initial={{ opacity: 0, scale: 0 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.5, delay: 1 }}
        className="fixed bottom-8 right-8 z-50"
      >
        <Link href="/policies">
          <div className="bg-gradient-to-r from-red-500 to-orange-500 hover:from-red-600 hover:to-orange-600 text-white p-4 rounded-full shadow-2xl hover:shadow-red-500/25 transition-all duration-300 hover:scale-110 group">
            <FileText className="w-6 h-6 group-hover:rotate-12 transition-transform" />
          </div>
        </Link>
      </motion.div>

      {/* Enhanced Footer */}
      <footer className="relative py-16 px-4 md:px-6 lg:px-8 bg-gradient-to-br from-gray-900 via-black to-gray-900">
        <div className="absolute inset-0 bg-gradient-to-r from-red-500/5 to-purple-500/5"></div>
        <div className="container mx-auto relative z-10">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-8">
            <div>
              <h3 className="text-xl font-bold text-red-400 mb-4">Inflynx Technologies</h3>
              <p className="text-gray-400 text-sm leading-relaxed">
                Empowering businesses with cutting-edge technology solutions and innovative digital transformation services.
              </p>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-red-300 mb-4">Quick Links</h3>
              <div className="space-y-2">
                <Link href="/" className="block text-gray-400 hover:text-red-400 transition-colors text-sm">→ Home</Link>
                <Link href="/policies" className="block text-gray-400 hover:text-red-400 transition-colors text-sm">→ All Policies</Link>
                <Link href="/contact-us" className="block text-gray-400 hover:text-red-400 transition-colors text-sm">→ Contact Support</Link>
              </div>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-red-300 mb-4">Legal</h3>
              <div className="space-y-2">
                <Link href="/terms-and-conditions" className="block text-gray-400 hover:text-red-400 transition-colors text-sm">→ Terms & Conditions</Link>
                <Link href="/cancellation-and-refund" className="block text-gray-400 hover:text-red-400 transition-colors text-sm">→ Refund Policy</Link>
                <Link href="/shipping-and-delivery" className="block text-gray-400 hover:text-red-400 transition-colors text-sm">→ Delivery Policy</Link>
              </div>
            </div>
          </div>
          <div className="border-t border-gray-700 pt-8 text-center">
            <p className="text-sm text-gray-400">
              ©2024 Inflynx Technologies. All rights reserved. | CIN: U63999OD2025PTC047988
            </p>
          </div>
        </div>
      </footer>
    </div>
  )
}
