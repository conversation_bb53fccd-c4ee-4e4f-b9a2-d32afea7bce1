'use client'

import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { ArrowLeft, FileText, Scale, AlertTriangle, Users, Gavel } from "lucide-react"
import { motion } from "framer-motion"

export default function TermsAndConditions() {
  return (
    <div className="flex flex-col min-h-screen bg-gray-900 text-white font-sans">
      <main className="flex-grow">
        {/* Header Section */}
        <section className="relative py-20 px-4 md:px-6 lg:px-8 bg-gradient-to-br from-black via-gray-900 to-black overflow-hidden">
          {/* Background Effects */}
          <div className="absolute inset-0 bg-gradient-to-r from-blue-500/5 via-purple-500/5 to-red-500/5"></div>
          <div className="absolute top-0 right-1/4 w-96 h-96 bg-blue-500/10 rounded-full blur-3xl"></div>
          <div className="absolute bottom-0 left-1/4 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl"></div>

          <div className="container mx-auto relative z-10">
            <motion.div
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="text-center max-w-4xl mx-auto"
            >
              <Link href="/" className="inline-flex items-center text-red-400 hover:text-red-300 mb-8 group">
                <ArrowLeft className="w-5 h-5 mr-2 group-hover:-translate-x-1 transition-transform" />
                Back to Home
              </Link>

              <div className="mb-6">
                <Scale className="w-16 h-16 text-red-400 mx-auto mb-4" />
              </div>

              <h1 className="text-5xl md:text-7xl font-bold mb-6 bg-clip-text text-transparent bg-gradient-to-r from-red-400 via-orange-500 to-red-600">
                Terms & Conditions
              </h1>
              <p className="text-xl md:text-2xl text-gray-300 leading-relaxed">
                Please read these terms and conditions carefully before using our services.
              </p>

              <div className="mt-8 flex justify-center">
                <div className="bg-gradient-to-r from-red-500/20 to-purple-500/20 rounded-full px-6 py-2 border border-red-500/30">
                  <span className="text-red-300 text-sm font-medium">Last Updated: January 25, 2025</span>
                </div>
              </div>
            </motion.div>
          </div>
        </section>

        {/* Content Section */}
        <section className="py-16 px-4 md:px-6 lg:px-8 bg-gray-900">
          <div className="container mx-auto max-w-4xl">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="space-y-8"
            >
              {/* Last Updated */}
              <div className="bg-gray-800/50 rounded-xl p-6 border border-gray-700/50">
                <p className="text-gray-300">
                  <strong>Last Updated:</strong> January 25, 2025
                </p>
              </div>

              {/* Acceptance of Terms */}
              <div className="bg-gradient-to-br from-gray-800/70 to-black/90 rounded-xl p-8 border border-gray-700/50">
                <div className="flex items-center mb-6">
                  <FileText className="w-8 h-8 text-red-400 mr-3" />
                  <h2 className="text-2xl font-bold text-red-400">Acceptance of Terms</h2>
                </div>
                <div className="space-y-4 text-gray-300">
                  <p>
                    By accessing and using the services provided by Inflynx Technologies ("Company", "we", "us", or "our"), 
                    you accept and agree to be bound by the terms and provision of this agreement.
                  </p>
                  <p>
                    If you do not agree to abide by the above, please do not use this service.
                  </p>
                </div>
              </div>

              {/* Services Description */}
              <div className="bg-gradient-to-br from-gray-800/70 to-black/90 rounded-xl p-8 border border-gray-700/50">
                <div className="flex items-center mb-6">
                  <Users className="w-8 h-8 text-red-400 mr-3" />
                  <h2 className="text-2xl font-bold text-red-400">Services Description</h2>
                </div>
                <div className="space-y-4 text-gray-300">
                  <p>Inflynx Technologies provides:</p>
                  <ul className="list-disc list-inside space-y-2 ml-4">
                    <li>Blockchain technology solutions and consulting</li>
                    <li>Artificial Intelligence and machine learning services</li>
                    <li>Internet of Things (IoT) development and implementation</li>
                    <li>Smart city infrastructure solutions</li>
                    <li>Custom software development and technical consulting</li>
                  </ul>
                </div>
              </div>

              {/* User Obligations */}
              <div className="bg-gradient-to-br from-gray-800/70 to-black/90 rounded-xl p-8 border border-gray-700/50">
                <div className="flex items-center mb-6">
                  <Scale className="w-8 h-8 text-red-400 mr-3" />
                  <h2 className="text-2xl font-bold text-red-400">User Obligations</h2>
                </div>
                <div className="space-y-4 text-gray-300">
                  <p>As a user of our services, you agree to:</p>
                  <ul className="list-disc list-inside space-y-2 ml-4">
                    <li>Provide accurate and complete information</li>
                    <li>Use our services only for lawful purposes</li>
                    <li>Not interfere with or disrupt our services</li>
                    <li>Respect intellectual property rights</li>
                    <li>Maintain the confidentiality of your account credentials</li>
                    <li>Comply with all applicable laws and regulations</li>
                  </ul>
                </div>
              </div>

              {/* Intellectual Property */}
              <div className="bg-gradient-to-br from-gray-800/70 to-black/90 rounded-xl p-8 border border-gray-700/50">
                <div className="flex items-center mb-6">
                  <Gavel className="w-8 h-8 text-red-400 mr-3" />
                  <h2 className="text-2xl font-bold text-red-400">Intellectual Property</h2>
                </div>
                <div className="space-y-4 text-gray-300">
                  <p>
                    All content, features, and functionality of our services, including but not limited to text, 
                    graphics, logos, icons, images, audio clips, video clips, data compilations, and software, 
                    are the exclusive property of Inflynx Technologies and are protected by copyright, trademark, 
                    and other intellectual property laws.
                  </p>
                  <p>
                    You may not reproduce, distribute, modify, create derivative works of, publicly display, 
                    publicly perform, republish, download, store, or transmit any of the material on our services 
                    without our prior written consent.
                  </p>
                </div>
              </div>

              {/* Limitation of Liability */}
              <div className="bg-gradient-to-br from-gray-800/70 to-black/90 rounded-xl p-8 border border-gray-700/50">
                <div className="flex items-center mb-6">
                  <AlertTriangle className="w-8 h-8 text-red-400 mr-3" />
                  <h2 className="text-2xl font-bold text-red-400">Limitation of Liability</h2>
                </div>
                <div className="space-y-4 text-gray-300">
                  <p>
                    In no event shall Inflynx Technologies, its directors, employees, partners, agents, suppliers, 
                    or affiliates be liable for any indirect, incidental, special, consequential, or punitive damages, 
                    including without limitation, loss of profits, data, use, goodwill, or other intangible losses, 
                    resulting from your use of our services.
                  </p>
                  <p>
                    Our total liability to you for all claims arising out of or relating to the use of or any 
                    inability to use any portion of the services shall not exceed the amount you paid to us for 
                    the services in the twelve (12) months prior to such claim.
                  </p>
                </div>
              </div>

              {/* Termination */}
              <div className="bg-gradient-to-br from-gray-800/70 to-black/90 rounded-xl p-8 border border-gray-700/50">
                <h2 className="text-2xl font-bold text-red-400 mb-6">Termination</h2>
                <div className="space-y-4 text-gray-300">
                  <p>
                    We may terminate or suspend your access to our services immediately, without prior notice or 
                    liability, for any reason whatsoever, including without limitation if you breach the Terms.
                  </p>
                  <p>
                    Upon termination, your right to use the services will cease immediately. All provisions of 
                    the Terms which by their nature should survive termination shall survive termination.
                  </p>
                </div>
              </div>

              {/* Governing Law */}
              <div className="bg-gradient-to-br from-gray-800/70 to-black/90 rounded-xl p-8 border border-gray-700/50">
                <h2 className="text-2xl font-bold text-red-400 mb-6">Governing Law</h2>
                <div className="space-y-4 text-gray-300">
                  <p>
                    These Terms shall be interpreted and governed by the laws of India. Any disputes arising 
                    under these Terms shall be subject to the exclusive jurisdiction of the courts in 
                    Bhubaneswar, Odisha, India.
                  </p>
                </div>
              </div>

              {/* Contact Information */}
              <div className="bg-gradient-to-br from-gray-800/70 to-black/90 rounded-xl p-8 border border-gray-700/50">
                <h2 className="text-2xl font-bold text-red-400 mb-6">Contact Us</h2>
                <div className="space-y-4 text-gray-300">
                  <p>
                    If you have any questions about these Terms and Conditions, please contact us at:
                  </p>
                  <div className="space-y-2">
                    <p><strong>Email:</strong> <EMAIL></p>
                    <p><strong>Address:</strong> Inflynx Technologies, Bhubaneswar, Odisha, India</p>
                    <p><strong>Phone:</strong> +91-XXXXXXXXXX</p>
                  </div>
                </div>
              </div>

              {/* Back to Home Button */}
              <div className="text-center pt-8">
                <Link href="/">
                  <Button className="bg-red-500 hover:bg-red-600 text-white px-8 py-3">
                    Back to Home
                  </Button>
                </Link>
              </div>
            </motion.div>
          </div>
        </section>
      </main>

      {/* Footer */}
      <footer className="py-8 px-4 md:px-6 lg:px-8 bg-gray-800">
        <div className="container mx-auto text-center text-sm text-gray-400">
          ©2024 Inflynx Technologies. All rights reserved.
        </div>
      </footer>
    </div>
  )
}
