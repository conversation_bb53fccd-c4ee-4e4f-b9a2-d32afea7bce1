'use client'

import Link from "next/link"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { ArrowLeft, Mail, Phone, MapPin, Clock, Send, MessageSquare } from "lucide-react"
import { motion } from "framer-motion"
import { useState } from "react"

export default function ContactUs() {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    subject: '',
    message: ''
  })

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    // Handle form submission here
    console.log('Form submitted:', formData)
    // Reset form
    setFormData({ name: '', email: '', subject: '', message: '' })
  }

  return (
    <div className="flex flex-col min-h-screen bg-gray-900 text-white font-sans">
      <main className="flex-grow">
        {/* Header Section */}
        <section className="py-16 px-4 md:px-6 lg:px-8 bg-black">
          <div className="container mx-auto">
            <motion.div
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="mb-8"
            >
              <Link href="/" className="inline-flex items-center text-red-400 hover:text-red-300 mb-6">
                <ArrowLeft className="w-5 h-5 mr-2" />
                Back to Home
              </Link>
              <h1 className="text-4xl md:text-5xl font-bold mb-4 bg-clip-text text-transparent bg-gradient-to-r from-red-400 to-orange-500">
                Contact Us
              </h1>
              <p className="text-xl text-gray-300">
                Get in touch with our team. We're here to help with your technology needs.
              </p>
            </motion.div>
          </div>
        </section>

        {/* Content Section */}
        <section className="py-16 px-4 md:px-6 lg:px-8 bg-gray-900">
          <div className="container mx-auto max-w-6xl">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
              {/* Contact Information */}
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
                className="space-y-8"
              >
                <div>
                  <h2 className="text-3xl font-bold text-red-400 mb-6">Get in Touch</h2>
                  <p className="text-gray-300 mb-8">
                    Ready to transform your business with cutting-edge technology? Our team of experts 
                    is here to discuss your project requirements and provide tailored solutions.
                  </p>
                </div>

                {/* Contact Cards */}
                <div className="space-y-6">
                  <div className="bg-gradient-to-br from-gray-800/70 to-black/90 rounded-xl p-6 border border-gray-700/50">
                    <div className="flex items-center mb-4">
                      <Mail className="w-6 h-6 text-red-400 mr-3" />
                      <h3 className="text-xl font-semibold text-red-400">Email</h3>
                    </div>
                    <div className="space-y-2 text-gray-300">
                      <p><strong>General Inquiries:</strong> <EMAIL></p>
                      <p><strong>Business Development:</strong> <EMAIL></p>
                      <p><strong>Technical Support:</strong> <EMAIL></p>
                      <p><strong>Careers:</strong> <EMAIL></p>
                    </div>
                  </div>

                  <div className="bg-gradient-to-br from-gray-800/70 to-black/90 rounded-xl p-6 border border-gray-700/50">
                    <div className="flex items-center mb-4">
                      <Phone className="w-6 h-6 text-red-400 mr-3" />
                      <h3 className="text-xl font-semibold text-red-400">Phone</h3>
                    </div>
                    <div className="space-y-2 text-gray-300">
                      <p><strong>Main Office:</strong> +91-XXXXXXXXXX</p>
                      <p><strong>Business Development:</strong> +91-XXXXXXXXXX</p>
                      <p><strong>Technical Support:</strong> +91-XXXXXXXXXX</p>
                    </div>
                  </div>

                  <div className="bg-gradient-to-br from-gray-800/70 to-black/90 rounded-xl p-6 border border-gray-700/50">
                    <div className="flex items-center mb-4">
                      <MapPin className="w-6 h-6 text-red-400 mr-3" />
                      <h3 className="text-xl font-semibold text-red-400">Address</h3>
                    </div>
                    <div className="text-gray-300">
                      <p>Inflynx Technologies</p>
                      <p>Bhubaneswar, Odisha, India</p>
                      <p>PIN: XXXXXX</p>
                    </div>
                  </div>

                  <div className="bg-gradient-to-br from-gray-800/70 to-black/90 rounded-xl p-6 border border-gray-700/50">
                    <div className="flex items-center mb-4">
                      <Clock className="w-6 h-6 text-red-400 mr-3" />
                      <h3 className="text-xl font-semibold text-red-400">Business Hours</h3>
                    </div>
                    <div className="space-y-2 text-gray-300">
                      <p><strong>Monday - Friday:</strong> 9:00 AM - 6:00 PM IST</p>
                      <p><strong>Saturday:</strong> 10:00 AM - 4:00 PM IST</p>
                      <p><strong>Sunday:</strong> Closed</p>
                      <p className="text-sm text-gray-400 mt-2">
                        Emergency support available 24/7 for enterprise clients
                      </p>
                    </div>
                  </div>
                </div>
              </motion.div>

              {/* Contact Form */}
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6, delay: 0.4 }}
                className="bg-gradient-to-br from-gray-800/70 to-black/90 rounded-xl p-8 border border-gray-700/50"
              >
                <div className="flex items-center mb-6">
                  <MessageSquare className="w-8 h-8 text-red-400 mr-3" />
                  <h2 className="text-2xl font-bold text-red-400">Send us a Message</h2>
                </div>

                <form onSubmit={handleSubmit} className="space-y-6">
                  <div>
                    <label htmlFor="name" className="block text-sm font-medium text-gray-300 mb-2">
                      Full Name *
                    </label>
                    <Input
                      type="text"
                      id="name"
                      name="name"
                      value={formData.name}
                      onChange={handleInputChange}
                      required
                      className="w-full bg-gray-700/50 border-gray-600 text-white placeholder-gray-400 focus:border-red-400"
                      placeholder="Enter your full name"
                    />
                  </div>

                  <div>
                    <label htmlFor="email" className="block text-sm font-medium text-gray-300 mb-2">
                      Email Address *
                    </label>
                    <Input
                      type="email"
                      id="email"
                      name="email"
                      value={formData.email}
                      onChange={handleInputChange}
                      required
                      className="w-full bg-gray-700/50 border-gray-600 text-white placeholder-gray-400 focus:border-red-400"
                      placeholder="Enter your email address"
                    />
                  </div>

                  <div>
                    <label htmlFor="subject" className="block text-sm font-medium text-gray-300 mb-2">
                      Subject *
                    </label>
                    <Input
                      type="text"
                      id="subject"
                      name="subject"
                      value={formData.subject}
                      onChange={handleInputChange}
                      required
                      className="w-full bg-gray-700/50 border-gray-600 text-white placeholder-gray-400 focus:border-red-400"
                      placeholder="What's this about?"
                    />
                  </div>

                  <div>
                    <label htmlFor="message" className="block text-sm font-medium text-gray-300 mb-2">
                      Message *
                    </label>
                    <textarea
                      id="message"
                      name="message"
                      value={formData.message}
                      onChange={handleInputChange}
                      required
                      rows={6}
                      className="w-full bg-gray-700/50 border border-gray-600 rounded-md px-3 py-2 text-white placeholder-gray-400 focus:border-red-400 focus:outline-none focus:ring-1 focus:ring-red-400"
                      placeholder="Tell us about your project or inquiry..."
                    />
                  </div>

                  <Button
                    type="submit"
                    className="w-full bg-red-500 hover:bg-red-600 text-white py-3 flex items-center justify-center"
                  >
                    <Send className="w-5 h-5 mr-2" />
                    Send Message
                  </Button>
                </form>

                <div className="mt-6 p-4 bg-gray-700/30 rounded-lg">
                  <p className="text-sm text-gray-300">
                    <strong>Response Time:</strong> We typically respond to inquiries within 24 hours during business days.
                    For urgent matters, please call us directly.
                  </p>
                </div>
              </motion.div>
            </div>

            {/* Company Information */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.6 }}
              className="mt-16 bg-gradient-to-br from-gray-800/70 to-black/90 rounded-xl p-8 border border-gray-700/50"
            >
              <h2 className="text-2xl font-bold text-red-400 mb-6">Company Information</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8 text-gray-300">
                <div>
                  <h3 className="text-lg font-semibold text-red-300 mb-3">Legal Information</h3>
                  <div className="space-y-2">
                    <p><strong>Company Name:</strong> Inflynx Technologies</p>
                    <p><strong>CIN Number:</strong> U63999OD2025PTC047988</p>
                    <p><strong>TAN Number:</strong> BBNI01878C</p>
                    <p><strong>Registration:</strong> Private Limited Company</p>
                  </div>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-red-300 mb-3">Services</h3>
                  <div className="space-y-2">
                    <p>• Blockchain Development & Consulting</p>
                    <p>• Artificial Intelligence Solutions</p>
                    <p>• IoT Development & Implementation</p>
                    <p>• Smart City Infrastructure</p>
                    <p>• Custom Software Development</p>
                  </div>
                </div>
              </div>
            </motion.div>

            {/* Back to Home Button */}
            <div className="text-center pt-8">
              <Link href="/">
                <Button className="bg-red-500 hover:bg-red-600 text-white px-8 py-3">
                  Back to Home
                </Button>
              </Link>
            </div>
          </div>
        </section>
      </main>

      {/* Footer */}
      <footer className="py-8 px-4 md:px-6 lg:px-8 bg-gray-800">
        <div className="container mx-auto text-center text-sm text-gray-400">
          ©2024 Inflynx Technologies. All rights reserved.
        </div>
      </footer>
    </div>
  )
}
