"use client";
import Link from "next/link";
import React, { useEffect, useState } from "react";
import { Button } from "./ui/button";
import { Cpu, Menu, X, ChevronDown } from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";
import { AnimatedGradientBorder } from "./ui/animated-gradient-border";
import { HoverFloatAnimation } from "./ui/floating-animation";

function Navbar() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);
  const [isPoliciesOpen, setIsPoliciesOpen] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      const show = window.scrollY > 50;
      if (show) {
        setIsScrolled(true);
      } else {
        setIsScrolled(false);
      }
    };

    document.addEventListener("scroll", handleScroll);
    return () => {
      document.removeEventListener("scroll", handleScroll);
    };
  }, []);

  const navLinks = [
    { title: "About", href: "#about" },
    { title: "Services", href: "#services" },
    { title: "Smart Cities & IoT", href: "#smart-cities-iot" },
    { title: "Contact", href: "#contact" },
  ];

  const policyLinks = [
    { title: "Privacy Policy", href: "/privacy-policy" },
    { title: "Terms & Conditions", href: "/terms-and-conditions" },
    { title: "Cancellation & Refund", href: "/cancellation-and-refund" },
    { title: "Shipping & Delivery", href: "/shipping-and-delivery" },
    { title: "Contact Us", href: "/contact-us" },
  ];

  return (
    <header
      className={`py-4 px-4 md:px-6 lg:px-8 top-0 sticky z-50 transition-all duration-300 ${
        isScrolled
          ? "bg-black/90 backdrop-blur-lg shadow-md"
          : "bg-black"
      }`}
    >
      <div className="container mx-auto flex justify-between items-center">
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.5 }}
        >
          <Link className="text-2xl font-bold flex items-center text-red-400" href="/">
            <motion.div
              whileHover={{ rotate: 5, scale: 1.1 }}
              transition={{ type: "spring", stiffness: 400, damping: 10 }}
            >
              <Cpu className="h-8 w-8 text-red-400 mr-2" />
            </motion.div>
            <span className="bg-clip-text text-transparent bg-gradient-to-r from-red-400 to-orange-500">
              Inflynx Technologies
            </span>
          </Link>
        </motion.div>

        <nav className="hidden md:flex space-x-1 items-center">
          {navLinks.map((link, i) => (
            <motion.div
              key={link.href}
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 0.1 * i }}
            >
              <HoverFloatAnimation amplitude={3} duration={0.2}>
                <Link
                  className="px-3 py-2 text-white hover:text-red-400 rounded-md transition-colors"
                  href={link.href}
                >
                  {link.title}
                </Link>
              </HoverFloatAnimation>
            </motion.div>
          ))}

          {/* Policies Dropdown */}
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: 0.1 * navLinks.length }}
            className="relative"
            onMouseEnter={() => setIsPoliciesOpen(true)}
            onMouseLeave={() => setIsPoliciesOpen(false)}
          >
            <button
              type="button"
              className="px-3 py-2 text-white hover:text-red-400 rounded-md transition-colors flex items-center"
            >
              Policies
              <ChevronDown className={`ml-1 h-4 w-4 transition-transform ${isPoliciesOpen ? 'rotate-180' : ''}`} />
            </button>

            <AnimatePresence>
              {isPoliciesOpen && (
                <motion.div
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -10 }}
                  transition={{ duration: 0.2 }}
                  className="absolute top-full left-0 mt-2 w-56 bg-black/90 backdrop-blur-lg rounded-lg border border-gray-700/50 shadow-lg z-50"
                >
                  {policyLinks.map((link, i) => (
                    <Link
                      key={link.href}
                      href={link.href}
                      className="block px-4 py-3 text-sm text-gray-300 hover:text-red-400 hover:bg-gray-800/50 transition-colors first:rounded-t-lg last:rounded-b-lg"
                    >
                      {link.title}
                    </Link>
                  ))}
                </motion.div>
              )}
            </AnimatePresence>
          </motion.div>
        </nav>

        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.5, delay: 0.4 }}
          className="hidden md:block"
        >
          <AnimatedGradientBorder
            borderWidth={1}
            gradientColors={["#FF0080", "#FF5C00", "#FF0000", "#7928CA"]}
          >
            <Button className="bg-black hover:bg-black/70 text-white">
              Get Started
            </Button>
          </AnimatedGradientBorder>
        </motion.div>

        <button
          type="button"
          className="md:hidden text-white z-50"
          onClick={() => setIsMenuOpen(!isMenuOpen)}
        >
          <motion.div
            initial={false}
            animate={{ rotate: isMenuOpen ? 90 : 0 }}
            transition={{ duration: 0.3 }}
          >
            {isMenuOpen ? (
              <X className="h-8 w-8" />
            ) : (
              <Menu className="h-8 w-8" />
            )}
          </motion.div>
        </button>
      </div>

      <AnimatePresence>
        {isMenuOpen && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: "auto" }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.3 }}
            className="md:hidden overflow-hidden"
          >
            <nav className="flex flex-col space-y-3 py-4 px-4 bg-black/90 backdrop-blur-lg rounded-b-lg">
              {navLinks.map((link, i) => (
                <motion.div
                  key={link.href}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: i * 0.1 }}
                >
                  <Link
                    className="block text-white hover:text-red-400 py-2"
                    href={link.href}
                    onClick={() => setIsMenuOpen(false)}
                  >
                    {link.title}
                  </Link>
                </motion.div>
              ))}

              {/* Policies Section in Mobile Menu */}
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: navLinks.length * 0.1 }}
                className="border-t border-gray-700 pt-3 mt-3"
              >
                <p className="text-red-400 font-semibold mb-2 text-sm">Policies</p>
                {policyLinks.map((link, i) => (
                  <Link
                    key={link.href}
                    className="block text-gray-300 hover:text-red-400 py-1 text-sm ml-2"
                    href={link.href}
                    onClick={() => setIsMenuOpen(false)}
                  >
                    {link.title}
                  </Link>
                ))}
              </motion.div>
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: navLinks.length * 0.1 }}
              >
                <AnimatedGradientBorder
                  borderWidth={1}
                  gradientColors={["#FF0080", "#FF5C00", "#FF0000", "#7928CA"]}
                >
                  <Button className="w-full bg-black hover:bg-black/70 text-white">
                    Get Started
                  </Button>
                </AnimatedGradientBorder>
              </motion.div>
            </nav>
          </motion.div>
        )}
      </AnimatePresence>
    </header>
  );
}

export default Navbar;
