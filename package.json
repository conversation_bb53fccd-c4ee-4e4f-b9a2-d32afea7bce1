{"name": "bhara<PERSON><PERSON><PERSON>", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@radix-ui/react-accordion": "^1.2.1", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-tabs": "^1.1.1", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "framer-motion": "^11.11.9", "lucide-react": "^0.453.0", "next": "14.2.20", "react": "^18", "react-dom": "^18", "tailwind-merge": "^2.5.4", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "eslint": "^8", "eslint-config-next": "14.2.20", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}