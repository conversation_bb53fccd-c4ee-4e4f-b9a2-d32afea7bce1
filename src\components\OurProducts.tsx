'use client'
import React from 'react'
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from './ui/card'
import { MessageSquareCode, Zap, Shield } from 'lucide-react'
import { motion } from 'framer-motion'
import { Animated3DCard } from './ui/animated-3d-card'
import { AnimatedSection, AnimatedSectionGroup } from './ui/animated-section'

const products = [
  {
    icon: <Shield className="w-12 h-12 mb-4 text-red-400" />,
    title: 'ChainVerdict',
    description: 'Blockchain backed, Generative AI powered solution for speedy justice delivery and transparent justice. This helps both the common public and justice stakeholders.',
    link: 'https://chainverdict.in',
  },
  {
    icon: <MessageSquareCode className="w-12 h-12 mb-4 text-red-400" />,
    title: 'BlockChat',
    description: 'Blockchain based, Generative AI powered chat application for secured communication channels like Defence, Space Research, Delegate communication and more.',
    link: 'https://icp-chat.vercel.app/', // replace with your actual link
  },
  {
    icon: <Zap className="w-12 h-12 mb-4 text-red-400" />,
    title: 'Vidy<PERSON><PERSON>hain',
    description: 'Blockchain based, Generative AI powered renewable energy management solution for efficient and sustainable energy generation and consumption.',
    link: 'https://vidyutchain-fe.vercel.app', // replace with your actual link
  },
]

function OurProducts() {
  return (
    <section id="services" className="py-16 px-4 md:px-6 lg:px-8 bg-gradient-to-b from-neutral-950 to-neutral-800 relative overflow-hidden">
      {/* Animated background dots */}
      <div className="absolute inset-0 overflow-hidden">
        {[...Array(20)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-1 h-1 bg-red-500 rounded-full opacity-20"
            style={{
              top: `${Math.random() * 100}%`,
              left: `${Math.random() * 100}%`,
            }}
            animate={{
              scale: [1, 1.5, 1],
              opacity: [0.2, 0.4, 0.2],
            }}
            transition={{
              duration: 4 + Math.random() * 4,
              repeat: Infinity,
              ease: "easeInOut",
              delay: Math.random() * 5,
            }}
          />
        ))}
      </div>
      
      <div className="container mx-auto relative z-10">
        <AnimatedSection>
          <h2 className="text-3xl md:text-4xl lg:text-5xl gradient-text font-bold mb-12 text-center">Our Products</h2>
        </AnimatedSection>
        
        <AnimatedSectionGroup className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8" staggerDelay={0.1}>
          {products.map((product, index) => (
            <a href={product.link} rel="noopener noreferrer" key={index} className="block">
              <Animated3DCard 
                className="rounded-xl overflow-hidden"
                rotationIntensity={15}
                glareEffect={true}
              >
                <div className="bg-gradient-to-br from-gray-800 to-gray-900 h-full rounded-xl border border-gray-700/50 overflow-hidden relative">
                  <div className="shimmer-effect absolute inset-0 opacity-10"></div>
                  <CardHeader className='flex flex-col justify-center items-center pt-8'>
                    <motion.div
                      whileHover={{ rotate: 10, scale: 1.2 }}
                      transition={{ type: "spring", stiffness: 400, damping: 10 }}
                      className="glow rounded-full p-4 bg-gray-700/30"
                    >
                      {product.icon}
                    </motion.div>
                    <CardTitle className="text-2xl md:text-3xl font-semibold text-white text-center mt-4 gradient-text">
                      {product.title}
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="pb-8">
                    <p className="text-gray-300">{product.description}</p>
                  </CardContent>
                </div>
              </Animated3DCard>
            </a>
          ))}
        </AnimatedSectionGroup>
      </div>
    </section>
  )
}

export default OurProducts
