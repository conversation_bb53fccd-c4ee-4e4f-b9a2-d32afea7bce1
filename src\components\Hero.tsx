'use client'
import React, { useState } from 'react'
import { FlipWords } from './ui/flip-words'
import { BackgroundBeamsWithCollision } from "@/components/ui/background-beams-with-collision";
import { AnimatedGradientBorderText } from './ui/animated-gradient-border';
import { ParticleBackground } from './ui/particle-background';
import { motion } from 'framer-motion';
import { Animated3DCard } from './ui/animated-3d-card';
import Image from 'next/image';

const featureCards = [
  {
    title: "Blockchain",
    icon: "/icons/blockchain-icon.svg",
    fallbackText: "BC",
    description: "Secured, transparent & immutable data transactions"
  },
  {
    title: "AI",
    icon: "/icons/ai-icon.svg",
    fallbackText: "AI",
    description: "Intelligent decision-making & predictive analytics"
  },
  {
    title: "IoT",
    icon: "/icons/iot-icon.svg",
    fallbackText: "IoT",
    description: "Connected devices & real-time data collection"
  }
];

function Hero() {
  const [hoveredCard, setHoveredCard] = useState<number | null>(null);
  const words = ['Blockchain Technology', "Artificial Intelligence", "IoT Solutions", "Smart City Infrastructure"]
  
  return (
    <div className="relative">
      <ParticleBackground 
        particleCount={40} 
        particleColors={["#FF0080", "#FF5C00", "#FF0000", "#7928CA"]} 
        minOpacity={0.1}
        maxOpacity={0.3}
        interactive={true}
      />
      
      {/* Animated background shapes */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <motion.div
          className="absolute w-[500px] h-[500px] rounded-full bg-gradient-to-r from-red-500/5 to-orange-500/5 blur-3xl"
          style={{ top: '-10%', right: '-20%' }}
          animate={{
            scale: [1, 1.2, 1],
            opacity: [0.2, 0.3, 0.2],
          }}
          transition={{ duration: 10, repeat: Infinity, ease: "easeInOut" }}
        />
        <motion.div
          className="absolute w-[300px] h-[300px] rounded-full bg-gradient-to-r from-purple-500/5 to-pink-500/5 blur-3xl"
          style={{ bottom: '-5%', left: '-10%' }}
          animate={{
            scale: [1.2, 1, 1.2],
            opacity: [0.2, 0.3, 0.2],
          }}
          transition={{ duration: 8, repeat: Infinity, ease: "easeInOut" }}
        />
      </div>
      
      <BackgroundBeamsWithCollision className="py-16 px-4 md:px-6 lg:px-8 bg-black text-center">
        <div className="container mx-auto relative z-10">
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <h1 className="text-3xl md:text-5xl lg:text-6xl font-bold mb-6 leading-tight text-white mx-0">
              Revolutionize Your Business with{" "}
              <span className="relative z-20 inline-block">
                <AnimatedGradientBorderText 
                  className="font-extrabold"
                  gradientColors={["#FF0080", "#FF5C00", "#FF0000", "#7928CA"]}
                >
                  <div className="text-transparent bg-clip-text bg-gradient-to-r from-orange-500 via-red-500 to-purple-600 relative z-30 min-h-[1.2em] p-1 bg-black/40 backdrop-blur-sm rounded">
                    <FlipWords duration={3000} words={words} className="text-white font-bold" />
                  </div>
                </AnimatedGradientBorderText>
              </span>
            </h1>
          </motion.div>
          
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.6, delay: 0.3 }}
          >
            <p className="text-lg md:text-xl lg:text-2xl mb-10 text-white max-w-3xl mx-auto">
              <span className="gradient-text font-bold">Harness the power</span> of cutting-edge technology to transform your operations and stay ahead of the competition.
            </p>
          </motion.div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-8">
            {featureCards.map((card, index) => (
              <Animated3DCard
                key={index}
                className="rounded-xl overflow-hidden"
                rotationIntensity={10}
                glareEffect={true}
                hoverScale={1.03}
              >
                <div 
                  className="bg-gradient-to-br from-gray-900/70 to-black/90 rounded-xl border border-gray-700/50 p-5 flex flex-col items-center text-center relative overflow-hidden min-h-[240px]"
                  onMouseEnter={() => setHoveredCard(index)}
                  onMouseLeave={() => setHoveredCard(null)}
                >
                  <div className="shimmer-effect absolute inset-0 opacity-10"></div>
                  
                  <div className="absolute inset-0 bg-gradient-to-r from-red-500/10 via-purple-500/10 to-blue-500/10 opacity-0 transition-opacity duration-300" 
                    style={{ opacity: hoveredCard === index ? 0.2 : 0 }} 
                  />
                  
                  <div className="relative z-10 mt-3 mb-4 flex-grow flex flex-col items-center justify-center">
                    <div className="glow rounded-full p-4 bg-gray-800/50 inline-block mb-4">
                      <Image 
                        src={card.icon} 
                        alt={card.title} 
                        width={64}
                        height={64}
                        className="w-16 h-16" 
                        onError={(e) => {
                          const target = e.currentTarget as HTMLImageElement;
                          target.src = `https://placehold.co/64x64?text=${card.fallbackText}`;
                        }}
                      />
                    </div>
                    <h3 className="text-2xl font-bold text-red-400 mb-2">{card.title}</h3>
                    <p className="text-white text-sm">{card.description}</p>
                  </div>
                </div>
              </Animated3DCard>
            ))}
          </div>
        </div>
      </BackgroundBeamsWithCollision>
    </div>
  )
}

export default Hero