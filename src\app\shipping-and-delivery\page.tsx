'use client'

import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { ArrowLeft, Truck, Package, Clock, Globe, CheckCircle, AlertTriangle } from "lucide-react"
import { motion } from "framer-motion"

export default function ShippingAndDelivery() {
  return (
    <div className="flex flex-col min-h-screen bg-gray-900 text-white font-sans">
      <main className="flex-grow">
        {/* Header Section */}
        <section className="relative py-20 px-4 md:px-6 lg:px-8 bg-gradient-to-br from-black via-gray-900 to-black overflow-hidden">
          {/* Background Effects */}
          <div className="absolute inset-0 bg-gradient-to-r from-purple-500/5 via-blue-500/5 to-cyan-500/5"></div>
          <div className="absolute top-0 right-1/3 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl"></div>
          <div className="absolute bottom-0 left-1/3 w-96 h-96 bg-cyan-500/10 rounded-full blur-3xl"></div>

          <div className="container mx-auto relative z-10">
            <motion.div
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="text-center max-w-4xl mx-auto"
            >
              <Link href="/" className="inline-flex items-center text-red-400 hover:text-red-300 mb-8 group">
                <ArrowLeft className="w-5 h-5 mr-2 group-hover:-translate-x-1 transition-transform" />
                Back to Home
              </Link>

              <div className="mb-6">
                <Truck className="w-16 h-16 text-red-400 mx-auto mb-4" />
              </div>

              <h1 className="text-4xl md:text-6xl font-bold mb-6 bg-clip-text text-transparent bg-gradient-to-r from-red-400 via-orange-500 to-red-600">
                Shipping & Delivery Policy
              </h1>
              <p className="text-xl md:text-2xl text-gray-300 leading-relaxed">
                Information about our service delivery methods and timelines.
              </p>

              <div className="mt-8 flex justify-center">
                <div className="bg-gradient-to-r from-red-500/20 to-purple-500/20 rounded-full px-6 py-2 border border-red-500/30">
                  <span className="text-red-300 text-sm font-medium">Last Updated: January 25, 2025</span>
                </div>
              </div>
            </motion.div>
          </div>
        </section>

        {/* Content Section */}
        <section className="py-16 px-4 md:px-6 lg:px-8 bg-gray-900">
          <div className="container mx-auto max-w-4xl">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="space-y-8"
            >
              {/* Last Updated */}
              <div className="bg-gray-800/50 rounded-xl p-6 border border-gray-700/50">
                <p className="text-gray-300">
                  <strong>Last Updated:</strong> January 25, 2025
                </p>
              </div>

              {/* Service Nature */}
              <div className="bg-gradient-to-br from-gray-800/70 to-black/90 rounded-xl p-8 border border-gray-700/50">
                <div className="flex items-center mb-6">
                  <Globe className="w-8 h-8 text-red-400 mr-3" />
                  <h2 className="text-2xl font-bold text-red-400">Nature of Our Services</h2>
                </div>
                <div className="space-y-4 text-gray-300">
                  <p>
                    Inflynx Technologies primarily provides digital services and solutions. Our "delivery" refers to 
                    the provision of digital services, software solutions, consultations, and technical deliverables 
                    rather than physical products.
                  </p>
                  <p>
                    This policy outlines how we deliver our services and any physical materials that may be part 
                    of our service offerings.
                  </p>
                </div>
              </div>

              {/* Digital Service Delivery */}
              <div className="bg-gradient-to-br from-gray-800/70 to-black/90 rounded-xl p-8 border border-gray-700/50">
                <div className="flex items-center mb-6">
                  <Package className="w-8 h-8 text-red-400 mr-3" />
                  <h2 className="text-2xl font-bold text-red-400">Digital Service Delivery</h2>
                </div>
                <div className="space-y-6 text-gray-300">
                  <div>
                    <h3 className="text-xl font-semibold text-red-300 mb-3">Consulting Services</h3>
                    <ul className="list-disc list-inside space-y-2 ml-4">
                      <li>Virtual consultations delivered via video conferencing platforms</li>
                      <li>Reports and documentation delivered electronically via email or secure portals</li>
                      <li>Immediate delivery upon completion of consultation sessions</li>
                    </ul>
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold text-red-300 mb-3">Software Solutions</h3>
                    <ul className="list-disc list-inside space-y-2 ml-4">
                      <li>Source code delivered via secure repositories (GitHub, GitLab, etc.)</li>
                      <li>Documentation and user manuals provided in digital format</li>
                      <li>Cloud-based deployments accessible immediately upon completion</li>
                      <li>Installation packages and setup instructions delivered electronically</li>
                    </ul>
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold text-red-300 mb-3">Training and Support</h3>
                    <ul className="list-disc list-inside space-y-2 ml-4">
                      <li>Online training sessions conducted via video platforms</li>
                      <li>Training materials and resources provided through learning management systems</li>
                      <li>Support documentation accessible through client portals</li>
                    </ul>
                  </div>
                </div>
              </div>

              {/* Delivery Timelines */}
              <div className="bg-gradient-to-br from-gray-800/70 to-black/90 rounded-xl p-8 border border-gray-700/50">
                <div className="flex items-center mb-6">
                  <Clock className="w-8 h-8 text-red-400 mr-3" />
                  <h2 className="text-2xl font-bold text-red-400">Delivery Timelines</h2>
                </div>
                <div className="space-y-6 text-gray-300">
                  <div>
                    <h3 className="text-xl font-semibold text-red-300 mb-3">Standard Timelines</h3>
                    <ul className="list-disc list-inside space-y-2 ml-4">
                      <li><strong>Consultation Reports:</strong> 2-5 business days after session completion</li>
                      <li><strong>Small Development Projects:</strong> 2-4 weeks depending on complexity</li>
                      <li><strong>Medium Development Projects:</strong> 1-3 months depending on scope</li>
                      <li><strong>Large Enterprise Solutions:</strong> 3-12 months with milestone-based delivery</li>
                      <li><strong>Technical Audits:</strong> 1-2 weeks after data collection</li>
                    </ul>
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold text-red-300 mb-3">Expedited Delivery</h3>
                    <ul className="list-disc list-inside space-y-2 ml-4">
                      <li>Rush delivery available for certain services with additional charges</li>
                      <li>Expedited timeline: 50% reduction in standard delivery time</li>
                      <li>Subject to team availability and project complexity</li>
                      <li>Additional charges: 25-50% of base service cost</li>
                    </ul>
                  </div>
                </div>
              </div>

              {/* Physical Materials */}
              <div className="bg-gradient-to-br from-gray-800/70 to-black/90 rounded-xl p-8 border border-gray-700/50">
                <div className="flex items-center mb-6">
                  <Truck className="w-8 h-8 text-red-400 mr-3" />
                  <h2 className="text-2xl font-bold text-red-400">Physical Materials (If Applicable)</h2>
                </div>
                <div className="space-y-6 text-gray-300">
                  <div>
                    <h3 className="text-xl font-semibold text-red-300 mb-3">Hardware Components</h3>
                    <ul className="list-disc list-inside space-y-2 ml-4">
                      <li>IoT devices and sensors (when part of solution package)</li>
                      <li>Development boards and prototyping materials</li>
                      <li>Custom hardware solutions</li>
                    </ul>
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold text-red-300 mb-3">Shipping Methods</h3>
                    <ul className="list-disc list-inside space-y-2 ml-4">
                      <li><strong>Domestic (India):</strong> 3-7 business days via courier services</li>
                      <li><strong>International:</strong> 7-21 business days depending on destination</li>
                      <li><strong>Express Shipping:</strong> Available at additional cost</li>
                      <li>Tracking information provided for all shipments</li>
                    </ul>
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold text-red-300 mb-3">Shipping Costs</h3>
                    <ul className="list-disc list-inside space-y-2 ml-4">
                      <li>Domestic shipping: ₹200-₹500 depending on weight and destination</li>
                      <li>International shipping: Calculated based on destination and weight</li>
                      <li>Free shipping for orders above ₹50,000</li>
                      <li>Express shipping: Additional 50% of standard shipping cost</li>
                    </ul>
                  </div>
                </div>
              </div>

              {/* Delivery Confirmation */}
              <div className="bg-gradient-to-br from-gray-800/70 to-black/90 rounded-xl p-8 border border-gray-700/50">
                <div className="flex items-center mb-6">
                  <CheckCircle className="w-8 h-8 text-red-400 mr-3" />
                  <h2 className="text-2xl font-bold text-red-400">Delivery Confirmation</h2>
                </div>
                <div className="space-y-4 text-gray-300">
                  <p>We ensure proper delivery confirmation through:</p>
                  <ul className="list-disc list-inside space-y-2 ml-4">
                    <li>Email notifications for digital deliveries</li>
                    <li>Client acknowledgment and sign-off for major deliverables</li>
                    <li>Tracking numbers and delivery confirmation for physical shipments</li>
                    <li>Follow-up communication to ensure successful receipt</li>
                    <li>Support for any delivery-related issues within 48 hours</li>
                  </ul>
                </div>
              </div>

              {/* Delivery Issues */}
              <div className="bg-gradient-to-br from-gray-800/70 to-black/90 rounded-xl p-8 border border-gray-700/50">
                <div className="flex items-center mb-6">
                  <AlertTriangle className="w-8 h-8 text-red-400 mr-3" />
                  <h2 className="text-2xl font-bold text-red-400">Delivery Issues and Support</h2>
                </div>
                <div className="space-y-4 text-gray-300">
                  <p>If you experience any delivery issues:</p>
                  <ul className="list-disc list-inside space-y-2 ml-4">
                    <li>Contact our support team immediately</li>
                    <li>Provide order/project reference number</li>
                    <li>We will investigate and resolve within 24-48 hours</li>
                    <li>Replacement or re-delivery at no additional cost for our errors</li>
                    <li>Compensation for delays caused by our team</li>
                  </ul>
                </div>
              </div>

              {/* Contact Information */}
              <div className="bg-gradient-to-br from-gray-800/70 to-black/90 rounded-xl p-8 border border-gray-700/50">
                <h2 className="text-2xl font-bold text-red-400 mb-6">Contact Us</h2>
                <div className="space-y-4 text-gray-300">
                  <p>
                    For questions about delivery or shipping, please contact us:
                  </p>
                  <div className="space-y-2">
                    <p><strong>Email:</strong> <EMAIL></p>
                    <p><strong>Address:</strong> Inflynx Technologies, Bhubaneswar, Odisha, India</p>
                    <p><strong>Phone:</strong> +91-6202620644</p>
                    <p><strong>Business Hours:</strong> Monday - Friday, 9:00 AM - 6:00 PM IST</p>
                  </div>
                </div>
              </div>

              {/* Back to Home Button */}
              <div className="text-center pt-8">
                <Link href="/">
                  <Button className="bg-red-500 hover:bg-red-600 text-white px-8 py-3">
                    Back to Home
                  </Button>
                </Link>
              </div>
            </motion.div>
          </div>
        </section>
      </main>

      {/* Footer */}
      <footer className="py-8 px-4 md:px-6 lg:px-8 bg-gray-800">
        <div className="container mx-auto text-center text-sm text-gray-400">
          ©2024 Inflynx Technologies. All rights reserved.
        </div>
      </footer>
    </div>
  )
}
