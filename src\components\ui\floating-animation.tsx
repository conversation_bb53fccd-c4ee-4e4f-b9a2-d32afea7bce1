"use client";

import React from "react";
import { motion } from "framer-motion";
import { cn } from "@/lib/utils";

interface FloatingAnimationProps {
  children: React.ReactNode;
  className?: string;
  amplitude?: number; // How far it moves
  duration?: number; // Duration of one cycle in seconds
  delay?: number; // Delay before animation starts
  direction?: "x" | "y" | "both"; // Direction of floating
  reverse?: boolean; // Reverse the animation direction
}

export const FloatingAnimation = ({
  children,
  className,
  amplitude = 10,
  duration = 4,
  delay = 0,
  direction = "y",
  reverse = false,
}: FloatingAnimationProps) => {
  const directionMultiplier = reverse ? -1 : 1;
  
  const getAnimationProps = () => {
    switch (direction) {
      case "x":
        return {
          x: [0, amplitude * directionMultiplier, 0],
        };
      case "y":
        return {
          y: [0, amplitude * directionMultiplier, 0],
        };
      case "both":
        return {
          x: [0, amplitude * 0.7 * directionMultiplier, 0],
          y: [0, amplitude * directionMultiplier, 0],
        };
      default:
        return {
          y: [0, amplitude * directionMultiplier, 0],
        };
    }
  };

  return (
    <motion.div
      className={cn("inline-block", className)}
      animate={getAnimationProps()}
      transition={{
        duration: duration,
        repeat: Infinity,
        repeatType: "reverse",
        ease: "easeInOut",
        delay: delay,
      }}
    >
      {children}
    </motion.div>
  );
};

// Hover animation that triggers a floating effect
export const HoverFloatAnimation = ({
  children,
  className,
  amplitude = 5,
  duration = 0.3,
}: Omit<FloatingAnimationProps, "delay" | "direction" | "reverse">) => {
  return (
    <motion.div
      className={cn("inline-block", className)}
      whileHover={{
        y: -amplitude,
        transition: {
          duration: duration,
          ease: "easeOut",
        },
      }}
    >
      {children}
    </motion.div>
  );
}; 