'use client'

import Link from "next/link"
import { Button } from "@/components/ui/button"
import { ArrowLeft, RefreshCw, CreditCard, Clock, AlertCircle, CheckCircle } from "lucide-react"
import { motion } from "framer-motion"

export default function CancellationAndRefund() {
  return (
    <div className="flex flex-col min-h-screen bg-gray-900 text-white font-sans">
      <main className="flex-grow">
        {/* Header Section */}
        <section className="py-16 px-4 md:px-6 lg:px-8 bg-black">
          <div className="container mx-auto">
            <motion.div
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="mb-8"
            >
              <Link href="/" className="inline-flex items-center text-red-400 hover:text-red-300 mb-6">
                <ArrowLeft className="w-5 h-5 mr-2" />
                Back to Home
              </Link>
              <h1 className="text-4xl md:text-5xl font-bold mb-4 bg-clip-text text-transparent bg-gradient-to-r from-red-400 to-orange-500">
                Cancellation and Refund Policy
              </h1>
              <p className="text-xl text-gray-300">
                Our policy regarding cancellations, refunds, and service modifications.
              </p>
            </motion.div>
          </div>
        </section>

        {/* Content Section */}
        <section className="py-16 px-4 md:px-6 lg:px-8 bg-gray-900">
          <div className="container mx-auto max-w-4xl">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="space-y-8"
            >
              {/* Last Updated */}
              <div className="bg-gray-800/50 rounded-xl p-6 border border-gray-700/50">
                <p className="text-gray-300">
                  <strong>Last Updated:</strong> January 25, 2025
                </p>
              </div>

              {/* Overview */}
              <div className="bg-gradient-to-br from-gray-800/70 to-black/90 rounded-xl p-8 border border-gray-700/50">
                <div className="flex items-center mb-6">
                  <RefreshCw className="w-8 h-8 text-red-400 mr-3" />
                  <h2 className="text-2xl font-bold text-red-400">Policy Overview</h2>
                </div>
                <div className="space-y-4 text-gray-300">
                  <p>
                    At Inflynx Technologies, we strive to provide exceptional services and ensure customer satisfaction. 
                    This policy outlines the terms and conditions for cancellations and refunds of our services.
                  </p>
                  <p>
                    We understand that circumstances may change, and we aim to be fair and transparent in our 
                    cancellation and refund processes.
                  </p>
                </div>
              </div>

              {/* Service Categories */}
              <div className="bg-gradient-to-br from-gray-800/70 to-black/90 rounded-xl p-8 border border-gray-700/50">
                <div className="flex items-center mb-6">
                  <CheckCircle className="w-8 h-8 text-red-400 mr-3" />
                  <h2 className="text-2xl font-bold text-red-400">Service Categories</h2>
                </div>
                <div className="space-y-6 text-gray-300">
                  <div>
                    <h3 className="text-xl font-semibold text-red-300 mb-3">Consulting Services</h3>
                    <ul className="list-disc list-inside space-y-2 ml-4">
                      <li>Blockchain consulting and strategy development</li>
                      <li>AI/ML consulting and implementation planning</li>
                      <li>IoT architecture and design consultation</li>
                      <li>Technical audits and assessments</li>
                    </ul>
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold text-red-300 mb-3">Development Services</h3>
                    <ul className="list-disc list-inside space-y-2 ml-4">
                      <li>Custom software development</li>
                      <li>Blockchain application development</li>
                      <li>AI/ML model development and training</li>
                      <li>IoT solution development and deployment</li>
                    </ul>
                  </div>
                </div>
              </div>

              {/* Cancellation Policy */}
              <div className="bg-gradient-to-br from-gray-800/70 to-black/90 rounded-xl p-8 border border-gray-700/50">
                <div className="flex items-center mb-6">
                  <Clock className="w-8 h-8 text-red-400 mr-3" />
                  <h2 className="text-2xl font-bold text-red-400">Cancellation Policy</h2>
                </div>
                <div className="space-y-6 text-gray-300">
                  <div>
                    <h3 className="text-xl font-semibold text-red-300 mb-3">Consulting Services</h3>
                    <ul className="list-disc list-inside space-y-2 ml-4">
                      <li>Cancellation allowed up to 48 hours before scheduled consultation</li>
                      <li>No cancellation fee for cancellations made within the allowed timeframe</li>
                      <li>Late cancellations (less than 48 hours) may incur a 25% cancellation fee</li>
                    </ul>
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold text-red-300 mb-3">Development Projects</h3>
                    <ul className="list-disc list-inside space-y-2 ml-4">
                      <li>Cancellation allowed before project commencement</li>
                      <li>Once development begins, cancellation terms depend on project milestone completion</li>
                      <li>Completed milestones are non-refundable</li>
                      <li>Ongoing milestone work may be subject to partial charges</li>
                    </ul>
                  </div>
                </div>
              </div>

              {/* Refund Policy */}
              <div className="bg-gradient-to-br from-gray-800/70 to-black/90 rounded-xl p-8 border border-gray-700/50">
                <div className="flex items-center mb-6">
                  <CreditCard className="w-8 h-8 text-red-400 mr-3" />
                  <h2 className="text-2xl font-bold text-red-400">Refund Policy</h2>
                </div>
                <div className="space-y-6 text-gray-300">
                  <div>
                    <h3 className="text-xl font-semibold text-red-300 mb-3">Eligible Refunds</h3>
                    <ul className="list-disc list-inside space-y-2 ml-4">
                      <li>Services not delivered as per agreed specifications</li>
                      <li>Technical issues preventing service delivery from our end</li>
                      <li>Cancellations made within the allowed timeframe</li>
                      <li>Duplicate payments or billing errors</li>
                    </ul>
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold text-red-300 mb-3">Refund Timeline</h3>
                    <ul className="list-disc list-inside space-y-2 ml-4">
                      <li>Refund requests must be submitted within 30 days of service completion</li>
                      <li>Processing time: 7-14 business days after approval</li>
                      <li>Refunds will be processed to the original payment method</li>
                      <li>Bank processing may take additional 3-5 business days</li>
                    </ul>
                  </div>
                </div>
              </div>

              {/* Non-Refundable Items */}
              <div className="bg-gradient-to-br from-gray-800/70 to-black/90 rounded-xl p-8 border border-gray-700/50">
                <div className="flex items-center mb-6">
                  <AlertCircle className="w-8 h-8 text-red-400 mr-3" />
                  <h2 className="text-2xl font-bold text-red-400">Non-Refundable Items</h2>
                </div>
                <div className="space-y-4 text-gray-300">
                  <p>The following items are generally non-refundable:</p>
                  <ul className="list-disc list-inside space-y-2 ml-4">
                    <li>Completed consulting sessions or delivered reports</li>
                    <li>Custom development work that has been delivered and accepted</li>
                    <li>Third-party licenses or services purchased on your behalf</li>
                    <li>Services cancelled outside the allowed cancellation period</li>
                    <li>Partial work on ongoing projects (unless due to our inability to deliver)</li>
                  </ul>
                </div>
              </div>

              {/* How to Request */}
              <div className="bg-gradient-to-br from-gray-800/70 to-black/90 rounded-xl p-8 border border-gray-700/50">
                <h2 className="text-2xl font-bold text-red-400 mb-6">How to Request Cancellation or Refund</h2>
                <div className="space-y-4 text-gray-300">
                  <p>To request a cancellation or refund:</p>
                  <ol className="list-decimal list-inside space-y-2 ml-4">
                    <li>Contact us via email at <strong><EMAIL></strong></li>
                    <li>Include your order/project reference number</li>
                    <li>Provide a detailed reason for the cancellation or refund request</li>
                    <li>Include any relevant documentation or evidence</li>
                    <li>Our team will review and respond within 2-3 business days</li>
                  </ol>
                </div>
              </div>

              {/* Contact Information */}
              <div className="bg-gradient-to-br from-gray-800/70 to-black/90 rounded-xl p-8 border border-gray-700/50">
                <h2 className="text-2xl font-bold text-red-400 mb-6">Contact Us</h2>
                <div className="space-y-4 text-gray-300">
                  <p>
                    For any questions regarding cancellations and refunds, please contact us:
                  </p>
                  <div className="space-y-2">
                    <p><strong>Email:</strong> <EMAIL></p>
                    <p><strong>Address:</strong> Inflynx Technologies, Bhubaneswar, Odisha, India</p>
                    <p><strong>Phone:</strong> +91-XXXXXXXXXX</p>
                    <p><strong>Business Hours:</strong> Monday - Friday, 9:00 AM - 6:00 PM IST</p>
                  </div>
                </div>
              </div>

              {/* Back to Home Button */}
              <div className="text-center pt-8">
                <Link href="/">
                  <Button className="bg-red-500 hover:bg-red-600 text-white px-8 py-3">
                    Back to Home
                  </Button>
                </Link>
              </div>
            </motion.div>
          </div>
        </section>
      </main>

      {/* Footer */}
      <footer className="py-8 px-4 md:px-6 lg:px-8 bg-gray-800">
        <div className="container mx-auto text-center text-sm text-gray-400">
          ©2024 Inflynx Technologies. All rights reserved.
        </div>
      </footer>
    </div>
  )
}
