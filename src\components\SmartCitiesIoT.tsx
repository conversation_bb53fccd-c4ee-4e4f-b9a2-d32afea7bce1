'use client'
import React, { useRef } from 'react'
import { <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>eader, CardTitle } from './ui/card'
import { Network, Building2, Cpu, Wifi, CloudCog, Car } from 'lucide-react'
import { <PERSON><PERSON> } from './ui/button'
import { AnimatedSection, AnimatedSectionGroup } from './ui/animated-section'
import { AnimatedGradientBorder } from './ui/animated-gradient-border'
import { Animated3DCard } from './ui/animated-3d-card'
import { motion, useScroll, useTransform } from 'framer-motion'

const solutions = [
  {
    icon: <Building2 className="w-12 h-12 mb-4 text-red-400" />,
    title: 'Smart Urban Infrastructure',
    description: 'AI-powered solutions for intelligent traffic management, waste management, and public safety that enhance urban living through real-time monitoring and analytics.',
  },
  {
    icon: <Network className="w-12 h-12 mb-4 text-red-400" />,
    title: 'Connected Communities',
    description: 'Blockchain-secured IoT networks that connect citizens, businesses, and government services to create transparent, efficient, and responsive smart cities.',
  },
  {
    icon: <Cpu className="w-12 h-12 mb-4 text-red-400" />,
    title: 'Intelligent Energy Systems',
    description: 'Smart grid technologies that optimize energy consumption, integrate renewable sources, and provide predictive maintenance for sustainable urban development.',
  },
  {
    icon: <Wifi className="w-12 h-12 mb-4 text-red-400" />,
    title: 'IoT Data Management',
    description: 'Secure, scalable platforms for collecting, processing, and analyzing data from thousands of IoT devices to drive data-informed decision making.',
  },
  {
    icon: <CloudCog className="w-12 h-12 mb-4 text-red-400" />,
    title: 'Smart City Consulting',
    description: 'End-to-end advisory services to help municipalities plan, implement, and optimize their smart city initiatives with proven methodologies.',
  },
  {
    icon: <Car className="w-12 h-12 mb-4 text-red-400" />,
    title: 'Smart Transportation Systems',
    description: 'Intelligent transportation solutions that optimize traffic flow, reduce congestion, and enhance mobility through autonomous vehicles and smart public transit.',
  },
]

function SmartCitiesIoT() {
  const ref = useRef(null);
  const { scrollYProgress } = useScroll({
    target: ref,
    offset: ["start end", "end start"]
  });
  
  const opacity = useTransform(scrollYProgress, [0, 0.2, 0.9, 1], [0.5, 1, 1, 0.5]);
  
  const scrollToContact = () => {
    const contactSection = document.getElementById('contact');
    if (contactSection) {
      contactSection.scrollIntoView({ behavior: 'smooth' });
    }
  };
  
  return (
    <section 
      ref={ref}
      id="smart-cities-iot" 
      className="py-20 px-4 md:px-6 lg:px-8 bg-gradient-to-b from-neutral-800 to-neutral-950 relative overflow-hidden"
    >
      {/* Animated background dots */}
      <div className="absolute inset-0 overflow-hidden">
        {[...Array(30)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-2 h-2 bg-red-500 rounded-full opacity-30"
            style={{
              top: `${Math.random() * 100}%`,
              left: `${Math.random() * 100}%`,
              filter: "blur(1px)",
            }}
            animate={{
              scale: [1, 1.5, 1],
              opacity: [0.3, 0.6, 0.3],
              boxShadow: [
                "0 0 5px 0px rgba(255, 0, 0, 0.3)",
                "0 0 20px 5px rgba(255, 0, 0, 0.5)",
                "0 0 5px 0px rgba(255, 0, 0, 0.3)",
              ],
            }}
            transition={{
              duration: 4 + Math.random() * 4,
              repeat: Infinity,
              ease: "easeInOut",
              delay: Math.random() * 5,
            }}
          />
        ))}
      </div>
      
      {/* Floating network lines */}
      <div className="absolute inset-0 overflow-hidden">
        {[...Array(8)].map((_, i) => {
          const startX = Math.random() * 100;
          const startY = Math.random() * 100;
          const endX = startX + (Math.random() - 0.5) * 50;
          const endY = startY + (Math.random() - 0.5) * 50;
          
          return (
            <motion.div
              key={`line-${i}`}
              className="absolute bg-gradient-to-r from-red-500/20 to-transparent h-[1px]"
              style={{
                top: `${startY}%`,
                left: `${startX}%`,
                width: `${Math.hypot(endX - startX, endY - startY)}%`,
                transform: `rotate(${Math.atan2(endY - startY, endX - startX) * (180 / Math.PI)}deg)`,
                transformOrigin: "left center",
              }}
              animate={{
                opacity: [0.1, 0.3, 0.1],
              }}
              transition={{
                duration: 3 + Math.random() * 2,
                repeat: Infinity,
                ease: "easeInOut",
              }}
            />
          );
        })}
      </div>
      
      <motion.div className="container mx-auto relative z-10" style={{ opacity }}>
        <AnimatedSection>
          <h2 className="text-3xl md:text-5xl lg:text-6xl gradient-text font-bold mb-8 text-center">
            Smart Cities & IoT Solutions
          </h2>
        </AnimatedSection>
        
        <AnimatedSection delay={0.2}>
          <div className="mb-16">
            <p className="text-gray-300 text-center max-w-4xl mx-auto mb-8 text-lg md:text-xl">
              At Inflynx Technologies, we&apos;re building the future of urban living through integrated smart city and IoT solutions. 
              Our blockchain-secured and AI-powered technologies transform urban infrastructure into responsive, 
              efficient systems that improve quality of life while reducing environmental impact.
            </p>
            
            <div className="flex justify-center">
              <AnimatedGradientBorder
                borderWidth={2}
                gradientColors={["#FF0080", "#FF5C00", "#FF0000", "#7928CA"]}
              >
                <Button 
                  className="bg-black hover:bg-black/70 text-white font-semibold px-8 py-6 text-lg"
                  onClick={scrollToContact}
                >
                  Schedule a Consultation
                </Button>
              </AnimatedGradientBorder>
            </div>
          </div>
        </AnimatedSection>
        
        <AnimatedSectionGroup className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8" staggerDelay={0.1}>
          {solutions.map((solution, index) => (
            <Animated3DCard 
              key={index} 
              className="rounded-xl overflow-hidden h-full"
              rotationIntensity={15}
              glareEffect={true}
            >
              <div className="bg-gradient-to-br from-gray-800 to-gray-900 h-full rounded-xl border border-gray-700/50 overflow-hidden relative flex flex-col min-h-[320px]">
                <div className="shimmer-effect absolute inset-0 opacity-10"></div>
                <CardHeader className='flex flex-col justify-center items-center pt-8'>
                  <motion.div
                    whileHover={{ rotate: 10, scale: 1.2 }}
                    transition={{ type: "spring", stiffness: 400, damping: 10 }}
                    className="glow rounded-full p-4 bg-gray-700/30"
                  >
                    {solution.icon}
                  </motion.div>
                  <CardTitle className="text-xl md:text-2xl font-semibold text-white text-center mt-4 gradient-text">
                    {solution.title}
                  </CardTitle>
                </CardHeader>
                <CardContent className="pb-8 flex-grow flex items-center">
                  <p className="text-gray-300">{solution.description}</p>
                </CardContent>
              </div>
            </Animated3DCard>
          ))}
        </AnimatedSectionGroup>
        
        <AnimatedSection delay={0.6}>
          <div className="mt-20 relative">
            <motion.div 
              className="absolute -top-10 -left-10 w-40 h-40 bg-red-500 rounded-full mix-blend-multiply filter blur-3xl opacity-10"
              animate={{
                scale: [1, 1.5, 1],
                opacity: [0.1, 0.2, 0.1],
              }}
              transition={{
                duration: 8,
                repeat: Infinity,
                ease: "easeInOut",
              }}
            />
            <motion.div 
              className="absolute -bottom-10 -right-10 w-40 h-40 bg-orange-500 rounded-full mix-blend-multiply filter blur-3xl opacity-10"
              animate={{
                scale: [1.5, 1, 1.5],
                opacity: [0.1, 0.2, 0.1],
              }}
              transition={{
                duration: 8,
                repeat: Infinity,
                ease: "easeInOut",
              }}
            />
        
          </div>
        </AnimatedSection>
      </motion.div>
    </section>
  )
}

export default SmartCitiesIoT 