'use client'

import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { ArrowLeft, FileText, Shield, RefreshCw, Truck, Phone } from "lucide-react"
import { motion } from "framer-motion"

export default function PoliciesIndex() {
  const policies = [
    {
      title: "Privacy Policy",
      description: "Learn how we collect, use, and protect your personal information.",
      href: "/privacy-policy",
      icon: Shield,
      color: "text-blue-400"
    },
    {
      title: "Terms and Conditions",
      description: "Our terms of service and user obligations when using our platform.",
      href: "/terms-and-conditions",
      icon: FileText,
      color: "text-green-400"
    },
    {
      title: "Cancellation and Refund",
      description: "Information about cancellations, refunds, and our refund process.",
      href: "/cancellation-and-refund",
      icon: RefreshCw,
      color: "text-yellow-400"
    },
    {
      title: "Shipping and Delivery",
      description: "Details about our service delivery methods and timelines.",
      href: "/shipping-and-delivery",
      icon: Truck,
      color: "text-purple-400"
    },
    {
      title: "Contact Us",
      description: "Get in touch with our team for support and inquiries.",
      href: "/contact-us",
      icon: Phone,
      color: "text-red-400"
    }
  ]

  return (
    <div className="flex flex-col min-h-screen bg-gray-900 text-white font-sans">
      <main className="flex-grow">
        {/* Header Section */}
        <section className="py-16 px-4 md:px-6 lg:px-8 bg-black">
          <div className="container mx-auto">
            <motion.div
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="mb-8"
            >
              <Link href="/" className="inline-flex items-center text-red-400 hover:text-red-300 mb-6">
                <ArrowLeft className="w-5 h-5 mr-2" />
                Back to Home
              </Link>
              <h1 className="text-4xl md:text-5xl font-bold mb-4 bg-clip-text text-transparent bg-gradient-to-r from-red-400 to-orange-500">
                Policies & Legal
              </h1>
              <p className="text-xl text-gray-300">
                Important information about our policies, terms, and legal requirements.
              </p>
            </motion.div>
          </div>
        </section>

        {/* Policies Grid */}
        <section className="py-16 px-4 md:px-6 lg:px-8 bg-gray-900">
          <div className="container mx-auto max-w-6xl">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
            >
              {policies.map((policy, index) => {
                const IconComponent = policy.icon
                return (
                  <motion.div
                    key={policy.href}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: 0.1 * index }}
                    className="group"
                  >
                    <Link href={policy.href}>
                      <div className="bg-gradient-to-br from-gray-800/70 to-black/90 rounded-xl p-8 border border-gray-700/50 hover:border-red-400/50 transition-all duration-300 h-full flex flex-col">
                        <div className="flex items-center mb-6">
                          <div className={`p-3 rounded-lg bg-gray-700/50 ${policy.color} group-hover:scale-110 transition-transform duration-300`}>
                            <IconComponent className="w-8 h-8" />
                          </div>
                        </div>
                        
                        <h3 className="text-2xl font-bold text-red-400 mb-4 group-hover:text-red-300 transition-colors">
                          {policy.title}
                        </h3>
                        
                        <p className="text-gray-300 mb-6 flex-grow">
                          {policy.description}
                        </p>
                        
                        <div className="flex items-center text-red-400 group-hover:text-red-300 transition-colors">
                          <span className="text-sm font-medium">Read More</span>
                          <ArrowLeft className="w-4 h-4 ml-2 rotate-180 group-hover:translate-x-1 transition-transform" />
                        </div>
                      </div>
                    </Link>
                  </motion.div>
                )
              })}
            </motion.div>

            {/* Company Information */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.8 }}
              className="mt-16 bg-gradient-to-br from-gray-800/70 to-black/90 rounded-xl p-8 border border-gray-700/50"
            >
              <h2 className="text-2xl font-bold text-red-400 mb-6">Company Information</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8 text-gray-300">
                <div>
                  <h3 className="text-lg font-semibold text-red-300 mb-3">Legal Details</h3>
                  <div className="space-y-2">
                    <p><strong>Company Name:</strong> Inflynx Technologies</p>
                    <p><strong>CIN Number:</strong> U63999OD2025PTC047988</p>
                    <p><strong>TAN Number:</strong> BBNI01878C</p>
                    <p><strong>Registration:</strong> Private Limited Company</p>
                    <p><strong>Location:</strong> Bhubaneswar, Odisha, India</p>
                  </div>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-red-300 mb-3">Quick Links</h3>
                  <div className="space-y-2">
                    <Link href="/contact-us" className="block text-gray-300 hover:text-red-400 transition-colors">
                      → Contact Support
                    </Link>
                    <Link href="/terms-and-conditions" className="block text-gray-300 hover:text-red-400 transition-colors">
                      → Terms of Service
                    </Link>
                    <Link href="/privacy-policy" className="block text-gray-300 hover:text-red-400 transition-colors">
                      → Privacy Policy
                    </Link>
                    <Link href="/" className="block text-gray-300 hover:text-red-400 transition-colors">
                      → Back to Homepage
                    </Link>
                  </div>
                </div>
              </div>
            </motion.div>

            {/* Back to Home Button */}
            <div className="text-center pt-8">
              <Link href="/">
                <Button className="bg-red-500 hover:bg-red-600 text-white px-8 py-3">
                  Back to Home
                </Button>
              </Link>
            </div>
          </div>
        </section>
      </main>

      {/* Footer */}
      <footer className="py-8 px-4 md:px-6 lg:px-8 bg-gray-800">
        <div className="container mx-auto text-center text-sm text-gray-400">
          ©2024 Inflynx Technologies. All rights reserved.
        </div>
      </footer>
    </div>
  )
}
