"use client";

import React, { useState, useRef } from "react";
import { motion } from "framer-motion";
import { cn } from "@/lib/utils";

interface Animated3DCardProps {
  children: React.ReactNode;
  className?: string;
  containerClassName?: string;
  glareEffect?: boolean;
  rotationIntensity?: number;
  shadowIntensity?: number;
  hoverScale?: number;
  backgroundGradient?: boolean;
}

export const Animated3DCard = ({
  children,
  className,
  containerClassName,
  glareEffect = true,
  rotationIntensity = 10,
  shadowIntensity = 1.5,
  hoverScale = 1.05,
  backgroundGradient = false,
}: Animated3DCardProps) => {
  const cardRef = useRef<HTMLDivElement>(null);
  const [rotateX, setRotateX] = useState(0);
  const [rotateY, setRotateY] = useState(0);
  const [mouseX, setMouseX] = useState(0);
  const [mouseY, setMouseY] = useState(0);
  const [isHovered, setIsHovered] = useState(false);

  const handleMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {
    if (!cardRef.current) return;
    
    const rect = cardRef.current.getBoundingClientRect();
    const width = rect.width;
    const height = rect.height;
    
    // Calculate mouse position relative to card center (in %)
    const centerX = (e.clientX - rect.left) / width - 0.5;
    const centerY = (e.clientY - rect.top) / height - 0.5;
    
    // Calculate rotation (higher value = more rotation)
    setRotateX(-centerY * rotationIntensity);
    setRotateY(centerX * rotationIntensity);
    
    // Store mouse position for glare effect
    setMouseX(centerX * 100 + 50);
    setMouseY(centerY * 100 + 50);
  };

  const handleMouseEnter = () => {
    setIsHovered(true);
  };

  const handleMouseLeave = () => {
    setIsHovered(false);
    setRotateX(0);
    setRotateY(0);
  };

  return (
    <div 
      className={cn("perspective-1000px w-full", containerClassName)}
      ref={cardRef}
      onMouseMove={handleMouseMove}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      <motion.div
        className={cn(
          "relative w-full h-full transform-style-3d transition-transform duration-200",
          className
        )}
        style={{
          rotateX: rotateX,
          rotateY: rotateY,
          scale: isHovered ? hoverScale : 1,
          boxShadow: isHovered
            ? `0 ${10 * shadowIntensity}px ${25 * shadowIntensity}px rgba(0, 0, 0, 0.15)`
            : "0 0 0 rgba(0, 0, 0, 0)",
        }}
      >
        {children}
        
        {glareEffect && isHovered && (
          <div
            className="absolute inset-0 z-10 pointer-events-none overflow-hidden rounded-xl"
            aria-hidden="true"
          >
            <div
              className="absolute inset-0 opacity-30 bg-gradient-radial from-white to-transparent blur-sm"
              style={{
                top: `${mouseY}%`,
                left: `${mouseX}%`,
                transform: "translate(-50%, -50%)",
                width: "200%",
                height: "200%",
              }}
            />
          </div>
        )}
        
        {backgroundGradient && isHovered && (
          <div
            className="absolute inset-0 -z-10 rounded-xl opacity-80"
            style={{
              background: "linear-gradient(45deg, #FF0080, #FF5C00, #FF0000, #7928CA)",
              backgroundSize: "200% 200%",
              animation: "gradientAnimation 2s ease infinite",
            }}
          />
        )}
      </motion.div>
    </div>
  );
};

export const AnimatedText = ({
  text,
  className,
  staggerDuration = 0.05,
  initialDelay = 0,
}: {
  text: string;
  className?: string;
  staggerDuration?: number;
  initialDelay?: number;
}) => {
  return (
    <div className={cn("overflow-hidden", className)}>
      <div className="flex">
        {text.split('').map((char, i) => (
          <motion.span
            key={i}
            initial={{ y: "100%", opacity: 0 }}
            animate={{ y: "0%", opacity: 1 }}
            transition={{
              duration: 0.5,
              ease: [0.33, 1, 0.68, 1],
              delay: initialDelay + i * staggerDuration,
            }}
            className={char === ' ' ? 'mx-1' : ''}
          >
            {char}
          </motion.span>
        ))}
      </div>
    </div>
  );
}; 