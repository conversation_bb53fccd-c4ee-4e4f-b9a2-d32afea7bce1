import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import {  <PERSON>, Zap, <PERSON><PERSON><PERSON>, Users, Star } from "lucide-react"
import Hero from "@/components/Hero"
import About from "@/components/About"
import OurProducts from "@/components/OurProducts"
import SmartCitiesIoT from "@/components/SmartCitiesIoT"



export default function BharatBlockAILanding() {
  return (
    <div className="flex flex-col min-h-screen bg-gray-900 text-white font-sans">

      <main className="flex-grow">
        {/* Hero Section */}
        <Hero />

        {/* About Us Section */}
        
        <About />
        {/* Products Section */}
        <OurProducts />

        {/* Smart Cities & IoT Solutions Section */}
        <SmartCitiesIoT />

        {/* Features Section */}
        <section className="py-16 px-4 md:px-6 lg:px-8 bg-gray-900">
          <div className="container mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold mb-12 text-center">Key Features</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              <div className="flex flex-col items-center text-center">
                <Zap className="w-12 h-12 mb-4 text-red-400" />
                <h3 className="text-xl font-semibold mb-2">Lightning Fast</h3>
                <p className="text-gray-300">Optimized performance for quick and efficient operations</p>
              </div>
              <div className="flex flex-col items-center text-center">
                <Shield className="w-12 h-12 mb-4 text-red-400" />
                <h3 className="text-xl font-semibold mb-2">Highly Secure</h3>
                <p className="text-gray-300">Advanced encryption and security measures to protect your data</p>
              </div>
              <div className="flex flex-col items-center text-center">
                <BarChart className="w-12 h-12 mb-4 text-red-400" />
                <h3 className="text-xl font-semibold mb-2">Scalable Solutions</h3>
                <p className="text-gray-300">Grow your business with our flexible and scalable technology</p>
              </div>
              <div className="flex flex-col items-center text-center">
                <Users className="w-12 h-12 mb-4 text-red-400" />
                <h3 className="text-xl font-semibold mb-2">24/7 Support</h3>
                <p className="text-gray-300">Round-the-clock assistance from our expert team</p>
              </div>
            </div>
          </div>
        </section>

        {/* Why Choose Us Section */}
        <section className="py-16 px-4 md:px-6 lg:px-8 bg-gray-800">
          <div className="container mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold mb-12 text-center">Why Choose Inflynx Technologies?</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <div>
                <h3 className="text-2xl font-semibold mb-4 flex items-center">
                  <Star className="w-6 h-6 mr-2 text-red-400" />
                  Expertise
                </h3>
                <p className="text-gray-300 mb-6">
                  Our team consists of industry-leading experts in blockchain, AI, and IoT technologies.
                </p>
                <h3 className="text-2xl font-semibold mb-4 flex items-center">
                  <Zap className="w-6 h-6 mr-2 text-red-400" />
                  Innovation
                </h3>
                <p className="text-gray-300 mb-6">
                  We are constantly pushing the boundaries of whats possible with cutting-edge solutions.
                </p>
              </div>
              <div>
                <h3 className="text-2xl font-semibold mb-4 flex items-center">
                  <Users className="w-6 h-6 mr-2 text-red-400" />
                  Client-Centric Approach
                </h3>
                <p className="text-gray-300 mb-6">
                  Your success is our priority. We work closely with you to ensure the best outcomes.
                </p>
                <h3 className="text-2xl font-semibold mb-4 flex items-center">
                  <Shield className="w-6 h-6 mr-2 text-red-400" />
                  Proven Track Record
                </h3>
                <p className="text-gray-300 mb-6">
                  Our successful case studies speak for themselves. Join our growing list of satisfied clients.
                </p>
              </div>
            </div>
          </div>
        </section>


        {/* Contact Us Section */}
        <section id="contact" className="py-16 px-4 md:px-6 lg:px-8 bg-gray-900">
          <div className="container mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold mb-12 text-center">Contact Us</h2>
            <div className="max-w-md mx-auto">
              <form className="space-y-4">
                <Input type="text" placeholder="Your Name" className="bg-gray-800 border-gray-700 text-white" />
                <Input type="email" placeholder="Your Email" className="bg-gray-800 border-gray-700 text-white" />
                <Input type="tel" placeholder="Your Phone" className="bg-gray-800 border-gray-700 text-white" />
                <textarea
                  className="w-full p-3 bg-gray-800 border border-gray-700 rounded-md text-white"
                  rows={4}
                  placeholder="Your Message"
                ></textarea>
                <Button className="w-full bg-red-500 hover:bg-red-600">Send Message</Button>
              </form>
            </div>
          </div>
        </section>
      </main>

      <footer className="py-8 px-4 md:px-6 lg:px-8 bg-gray-800">
        <div className="container mx-auto flex flex-col md:flex-row justify-between items-center">
          <div className="mb-4 md:mb-0">
            <Link className="text-2xl font-bold text-red-400" href="/">
              Inflynx Technologies
            </Link>
            <p className="text-sm text-gray-400 mt-2">Revolutionizing industries with Blockchain, AI, and IoT solutions</p>
          </div>
          <nav className="flex flex-wrap justify-center md:justify-end gap-4">
            
            <Link className="text-sm text-gray-300 hover:text-red-400" href="#about">
              CIN Number: U63999OD2025PTC047988
            </Link>
            <Link className="text-sm text-gray-300 hover:text-red-400" href="#about">
              TAN Number: BBNI01878C
            </Link>
            <Link className="text-sm text-gray-300 hover:text-red-400" href="#services">
              About
            </Link>
            <Link className="text-sm text-gray-300 hover:text-red-400" href="#case-studies">
              Case Studies
            </Link>
            <Link className="text-sm text-gray-300 hover:text-red-400" href="#contact">
              Contact
            </Link>
          </nav>
        </div>
        <div className="container mx-auto mt-8 pt-4 border-t border-gray-700 text-center text-sm text-gray-400">
          ©2024 Inflynx Technologies. All rights reserved.
        </div>
      </footer>
    </div>
  )
}