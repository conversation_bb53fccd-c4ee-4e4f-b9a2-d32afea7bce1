"use client";

import React from "react";
import { cn } from "@/lib/utils";
import { motion } from "framer-motion";

interface AnimatedGradientBorderProps {
  children: React.ReactNode;
  className?: string;
  containerClassName?: string;
  borderWidth?: number;
  duration?: number;
  delay?: number;
  gradientColors?: string[];
}

export const AnimatedGradientBorder = ({
  children,
  className,
  containerClassName,
  borderWidth: _borderWidth = 2,
  duration = 4,
  delay = 0,
  gradientColors = ["#FF0080", "#FF5C00", "#FF0000", "#7928CA"]
}: AnimatedGradientBorderProps) => {
  return (
    <div className={cn("relative rounded-lg p-[1px] overflow-hidden group", containerClassName)}>
      <motion.div
        className="absolute inset-0 rounded-lg z-[1]"
        style={{
          background: `linear-gradient(45deg, ${gradientColors.join(', ')})`,
          backgroundSize: "400% 400%",
        }}
        animate={{
          backgroundPosition: ["0% 0%", "100% 100%", "0% 0%"],
        }}
        transition={{
          duration: duration,
          ease: "linear",
          repeat: Infinity,
          delay: delay,
        }}
      />
      <div
        className={cn(
          "relative h-full w-full rounded-lg bg-black z-[2]",
          className
        )}
        style={{
          padding: _borderWidth,
        }}
      >
        {children}
      </div>
    </div>
  );
};

export const AnimatedGradientBorderText = ({
  children,
  className,
  borderWidth: _borderWidth = 2,
  duration = 4,
  delay = 0,
  gradientColors = ["#FF0080", "#FF5C00", "#FF0000", "#7928CA"]
}: Omit<AnimatedGradientBorderProps, "containerClassName">) => {
  const paddingValue = _borderWidth > 0 ? true : false;
  
  return (
    <span className={cn("relative inline-block", className)}>
      <span className="relative z-10">{children}</span>
      <motion.span
        className="absolute inset-0 z-0"
        style={{
          background: `linear-gradient(45deg, ${gradientColors.join(', ')})`,
          backgroundSize: "400% 400%",
          WebkitBackgroundClip: "text",
          WebkitTextFillColor: "transparent",
          padding: paddingValue ? '0px' : '0px',
        }}
        animate={{
          backgroundPosition: ["0% 0%", "100% 100%", "0% 0%"],
        }}
        transition={{
          duration: duration,
          ease: "linear",
          repeat: Infinity,
          delay: delay,
        }}
      >
        {children}
      </motion.span>
    </span>
  );
}; 