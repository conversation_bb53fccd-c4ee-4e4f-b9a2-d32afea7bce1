'use client'
import React from 'react'
import Image from 'next/image'
import { AnimatedSection } from './ui/animated-section';
import { Button } from './ui/button';

function About() {
  const aboutContent = {
    title: "About Inflynx Technologies",
    description: `Inflynx Technologies (est. 2025) is the parent company of ChainVerdict and VidyutChain, focused on delivering cutting-edge solutions using Blockchain, AI, and IoT. We specialize in building secure, scalable, and impactful tech for legal, energy, and public-sector innovation.

Backed by a diverse team of engineers, data scientists, and domain experts, Inflynx is committed to solving real-world problems with tailored, high-performance systems that drive efficiency and long-term value.

`,
    approach: {
      title: "Our Approach",
      description: "We employ a rigorous methodology that combines industry best practices with innovative approaches to ensure optimal outcomes for every project we undertake.",
      steps: [
        {
          title: "Discovery & Analysis",
          description: "Thorough assessment of business requirements and technical constraints"
        },
        {
          title: "Solution Design",
          description: "Collaborative development of scalable, future-proof architectural frameworks"
        },
        {
          title: "Implementation",
          description: "Agile delivery with continuous testing and quality assurance"
        },
        {
          title: "Deployment & Support",
          description: "Seamless integration with comprehensive training and ongoing maintenance"
        }
      ]
    },
    values: [
      {
        title: "Innovation",
        description: "Continually exploring emerging technologies to deliver forward-thinking solutions"
      },
      {
        title: "Excellence",
        description: "Maintaining rigorous standards across all aspects of our work"
      },
      {
        title: "Integrity", 
        description: "Operating with transparency and ethical principles in all client relationships"
      }
    ]
  };
  
  const scrollToContact = () => {
    const contactSection = document.getElementById('contact');
    if (contactSection) {
      contactSection.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <section id="about" className="py-20 px-4 md:px-6 lg:px-8 bg-gradient-to-b from-black to-gray-900 relative overflow-hidden">
      {/* Subtle background pattern */}
      <div className="absolute inset-0 overflow-hidden opacity-10">
        <svg width="100%" height="100%" xmlns="http://www.w3.org/2000/svg">
          <defs>
            <pattern id="grid" width="40" height="40" patternUnits="userSpaceOnUse">
              <path d="M 40 0 L 0 0 0 40" fill="none" stroke="rgba(255,75,75,0.2)" strokeWidth="1" />
            </pattern>
          </defs>
          <rect width="100%" height="100%" fill="url(#grid)" />
        </svg>
      </div>

      <div className="container mx-auto relative z-10">
        <AnimatedSection>
          <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-12 text-center">
            <span className="text-white">
              {aboutContent.title}
            </span>
          </h2>
        </AnimatedSection>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-start">
          <AnimatedSection className="order-2 lg:order-1">
            <div className="space-y-8 text-white">
              {aboutContent.description.split('\n\n').map((paragraph, index) => (
                <p key={index} className="text-lg leading-relaxed text-gray-100">
                  {paragraph}
                </p>
              ))}
              
              <div className="mt-10">
                <h3 className="text-2xl font-semibold text-red-400 mb-4">{aboutContent.approach.title}</h3>
                <p className="text-gray-200 mb-6">{aboutContent.approach.description}</p>
                
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  {aboutContent.approach.steps.map((step, index) => (
                    <div key={index} className="bg-gray-800/30 border border-gray-700/50 rounded-lg p-4 flex">
                      <div className="mr-3 mt-1 flex-shrink-0">
                        <div className="w-6 h-6 rounded-full bg-red-400/20 flex items-center justify-center text-red-400 font-semibold">
                          {index + 1}
                        </div>
                      </div>
                      <div>
                        <h4 className="text-lg font-medium text-red-400">{step.title}</h4>
                        <p className="text-gray-300 text-sm">{step.description}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
              
              <div className="mt-10">
                <h3 className="text-2xl font-semibold text-white mb-4">Core Values</h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  {aboutContent.values.map((value, index) => (
                    <div key={index} className="bg-gradient-to-br from-gray-800/40 to-gray-900/40 border border-gray-700/50 rounded-lg p-5">
                      <h3 className="text-xl font-bold text-red-400 mb-2">{value.title}</h3>
                      <p className="text-gray-300 text-sm">{value.description}</p>
                    </div>
                  ))}
                </div>
              </div>

              <div className="mt-10 flex justify-center">
                <Button 
                  onClick={scrollToContact}
                  className="px-8 py-6 bg-red-500 hover:bg-red-600 text-white font-semibold text-lg rounded-lg transition-all shadow-lg hover:shadow-xl"
                >
                  Schedule Consultation
                </Button>
              </div>
            </div>
          </AnimatedSection>
          
          <AnimatedSection className="order-1 lg:order-2">
            <div className="sticky top-24">
              <div className="relative w-full overflow-hidden rounded-xl border border-gray-700/50 shadow-xl shadow-black/30">
                <Image 
                  src='/about-inflynx.jpg' 
                  alt="Inflynx Technologies Team" 
                  className="object-cover w-full h-full"
                  width={600} 
                  height={450}
                  priority
                  unoptimized={true}
                  onError={(e) => {
                    console.error("Image failed to load:", e);
                    e.currentTarget.src = '/aboutUsImage.png';
                  }}
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/40 to-transparent flex flex-col justify-end">
                  <div className="p-6">
                    <h3 className="text-xl font-bold text-white mb-2">Enterprise Technology Solutions</h3>
                    <p className="text-gray-300 text-sm leading-relaxed">
                      Our team combines technical expertise with industry knowledge to deliver solutions that empower organizations to achieve their strategic objectives.
                    </p>
                  </div>
                </div>
              </div>
              
              <div className="mt-6 bg-gray-800/30 border border-gray-700/50 rounded-lg p-5">
                <h3 className="text-xl font-bold text-red-400 mb-3">Industry Recognition</h3>
                <ul className="space-y-2 text-gray-300">
                  <li className="flex items-start">
                    <div className="mr-2 text-red-400">•</div>
                    <span>Named Top Blockchain Innovation Company 2023</span>
                  </li>
                  <li className="flex items-start">
                    <div className="mr-2 text-red-400">•</div>
                    <span>Awarded for Excellence in IoT Solutions Implementation</span>
                  </li>
                  <li className="flex items-start">
                    <div className="mr-2 text-red-400">•</div>
                    <span>Recognized for Corporate Social Responsibility initiatives</span>
                  </li>
                </ul>
              </div>
            </div>
          </AnimatedSection>
        </div>
      </div>
    </section>
  )
}

export default About